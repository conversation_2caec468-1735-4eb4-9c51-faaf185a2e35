import { BaseRepository } from './BaseRepository';
import { UserProfile } from '@/shared/types';

/**
 * 用户档案Repository
 */
export class UserRepository extends BaseRepository<UserProfile> {
  constructor() {
    super('userProfiles');
  }

  /**
   * 根据条件查找用户
   */
  async findBy(criteria: Partial<UserProfile>): Promise<UserProfile[]> {
    const allUsers = await this.findAll();
    
    return allUsers.filter(user => {
      return Object.entries(criteria).every(([key, value]) => {
        return user[key as keyof UserProfile] === value;
      });
    });
  }

  /**
   * 根据邮箱查找用户
   */
  async findByEmail(email: string): Promise<UserProfile | null> {
    const users = await this.findBy({ email });
    return users.length > 0 ? users[0] : null;
  }

  /**
   * 获取当前活跃用户（假设只有一个用户）
   */
  async getCurrentUser(): Promise<UserProfile | null> {
    const users = await this.findAll();
    return users.length > 0 ? users[0] : null;
  }

  /**
   * 更新用户偏好设置
   */
  async updatePreferences(userId: string, preferences: Partial<UserProfile['preferences']>): Promise<UserProfile> {
    const user = await this.findById(userId);
    if (!user) {
      throw new Error('用户不存在');
    }

    return await this.update(userId, {
      preferences: {
        ...user.preferences,
        ...preferences
      }
    });
  }

  /**
   * 更新三餐分配比例
   */
  async updateMealRatios(userId: string, mealRatios: UserProfile['mealRatios']): Promise<UserProfile> {
    return await this.update(userId, { mealRatios });
  }

  /**
   * 更新目标设置
   */
  async updateGoals(userId: string, goals: {
    targetWeight?: number;
    targetDays?: number;
    activityLevel?: UserProfile['activityLevel'];
  }): Promise<UserProfile> {
    return await this.update(userId, goals);
  }
}

// 创建全局实例
export const userRepository = new UserRepository();