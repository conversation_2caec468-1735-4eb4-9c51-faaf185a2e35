import { BaseRepository } from './BaseRepository';
import { FoodEntry, FoodEntryFilter, MealType } from '@/shared/types';
import { formatDate, isInSamePeriod } from '@/shared/utils';

/**
 * 食物条目Repository
 */
export class FoodRepository extends BaseRepository<FoodEntry> {
  constructor() {
    super('foodEntries');
  }

  /**
   * 根据条件查找食物条目
   */
  async findBy(criteria: Partial<FoodEntry>): Promise<FoodEntry[]> {
    const allEntries = await this.findAll();
    
    return allEntries.filter(entry => {
      return Object.entries(criteria).every(([key, value]) => {
        if (key === 'consumedAt' && value instanceof Date) {
          return isInSamePeriod(entry.consumedAt, value, 'day');
        }
        return entry[key as keyof FoodEntry] === value;
      });
    });
  }

  /**
   * 根据日期查找食物条目
   */
  async findByDate(date: Date): Promise<FoodEntry[]> {
    const allEntries = await this.findAll();
    
    return allEntries.filter(entry => 
      isInSamePeriod(entry.consumedAt, date, 'day')
    ).sort((a, b) => a.consumedAt.getTime() - b.consumedAt.getTime());
  }

  /**
   * 根据日期范围查找食物条目
   */
  async findByDateRange(startDate: Date, endDate: Date): Promise<FoodEntry[]> {
    const allEntries = await this.findAll();
    
    return allEntries.filter(entry => {
      const entryDate = new Date(entry.consumedAt);
      return entryDate >= startDate && entryDate <= endDate;
    }).sort((a, b) => a.consumedAt.getTime() - b.consumedAt.getTime());
  }

  /**
   * 根据用餐类型查找食物条目
   */
  async findByMealType(mealType: MealType, date?: Date): Promise<FoodEntry[]> {
    let entries = await this.findBy({ mealType });
    
    if (date) {
      entries = entries.filter(entry => 
        isInSamePeriod(entry.consumedAt, date, 'day')
      );
    }
    
    return entries.sort((a, b) => a.consumedAt.getTime() - b.consumedAt.getTime());
  }

  /**
   * 根据筛选条件查找食物条目
   */
  async findByFilter(filter: FoodEntryFilter): Promise<FoodEntry[]> {
    let entries = await this.findAll();

    // 日期范围筛选
    if (filter.startDate || filter.endDate) {
      entries = entries.filter(entry => {
        const entryDate = new Date(entry.consumedAt);
        if (filter.startDate && entryDate < filter.startDate) return false;
        if (filter.endDate && entryDate > filter.endDate) return false;
        return true;
      });
    }

    // 用餐类型筛选
    if (filter.mealType) {
      entries = entries.filter(entry => entry.mealType === filter.mealType);
    }

    // 卡路里范围筛选
    if (filter.minCalories !== undefined) {
      entries = entries.filter(entry => entry.calories >= filter.minCalories!);
    }
    if (filter.maxCalories !== undefined) {
      entries = entries.filter(entry => entry.calories <= filter.maxCalories!);
    }

    // 收藏筛选
    if (filter.isFavorite !== undefined) {
      entries = entries.filter(entry => entry.isFavorite === filter.isFavorite);
    }

    // 搜索词筛选
    if (filter.searchTerm) {
      const searchTerm = filter.searchTerm.toLowerCase();
      entries = entries.filter(entry => 
        entry.name.toLowerCase().includes(searchTerm) ||
        (entry.notes && entry.notes.toLowerCase().includes(searchTerm))
      );
    }

    return entries.sort((a, b) => b.consumedAt.getTime() - a.consumedAt.getTime());
  }

  /**
   * 获取收藏的食物条目
   */
  async getFavorites(): Promise<FoodEntry[]> {
    return await this.findBy({ isFavorite: true });
  }

  /**
   * 切换收藏状态
   */
  async toggleFavorite(id: string): Promise<FoodEntry> {
    const entry = await this.findById(id);
    if (!entry) {
      throw new Error('食物条目不存在');
    }

    return await this.update(id, { isFavorite: !entry.isFavorite });
  }

  /**
   * 获取最近使用的食物
   */
  async getRecentFoods(limit: number = 10): Promise<FoodEntry[]> {
    const allEntries = await this.findAll();
    
    // 按食物名称分组，获取最新的条目
    const foodMap = new Map<string, FoodEntry>();
    
    allEntries
      .sort((a, b) => b.consumedAt.getTime() - a.consumedAt.getTime())
      .forEach(entry => {
        if (!foodMap.has(entry.name)) {
          foodMap.set(entry.name, entry);
        }
      });

    return Array.from(foodMap.values()).slice(0, limit);
  }

  /**
   * 获取每日卡路里统计
   */
  async getDailyCalorieStats(date: Date): Promise<{
    total: number;
    byMeal: Record<MealType, number>;
  }> {
    const entries = await this.findByDate(date);
    
    const stats = {
      total: 0,
      byMeal: {
        breakfast: 0,
        lunch: 0,
        dinner: 0,
        snack: 0
      } as Record<MealType, number>
    };

    entries.forEach(entry => {
      stats.total += entry.calories;
      stats.byMeal[entry.mealType] += entry.calories;
    });

    return stats;
  }

  /**
   * 获取营养素统计
   */
  async getNutritionStats(date: Date): Promise<{
    protein: number;
    fat: number;
    carbs: number;
    fiber: number;
    sugar: number;
  }> {
    const entries = await this.findByDate(date);
    
    const stats = {
      protein: 0,
      fat: 0,
      carbs: 0,
      fiber: 0,
      sugar: 0
    };

    entries.forEach(entry => {
      if (entry.nutrition) {
        stats.protein += entry.nutrition.protein || 0;
        stats.fat += entry.nutrition.fat || 0;
        stats.carbs += entry.nutrition.carbs || 0;
        stats.fiber += entry.nutrition.fiber || 0;
        stats.sugar += entry.nutrition.sugar || 0;
      }
    });

    return stats;
  }

  /**
   * 批量删除过期数据
   */
  async deleteOldEntries(daysToKeep: number = 365): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
    
    const allEntries = await this.findAll();
    const oldEntries = allEntries.filter(entry => 
      new Date(entry.consumedAt) < cutoffDate
    );

    for (const entry of oldEntries) {
      await this.delete(entry.id);
    }

    return oldEntries.length;
  }
}

// 创建全局实例
export const foodRepository = new FoodRepository();