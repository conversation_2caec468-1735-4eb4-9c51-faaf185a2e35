import { dbManager } from '../database';
import { BaseEntity } from '@/shared/types';

/**
 * 基础Repository类
 */
export abstract class BaseRepository<T extends BaseEntity> {
  protected storeName: string;

  constructor(storeName: string) {
    this.storeName = storeName;
  }

  /**
   * 创建实体
   */
  async create(entity: Omit<T, 'id' | 'createdAt' | 'updatedAt'>): Promise<T> {
    const db = dbManager.getDB();
    
    const newEntity: T = {
      ...entity,
      id: this.generateId(),
      createdAt: new Date(),
      updatedAt: new Date()
    } as T;

    await db.add(this.storeName, newEntity);
    return newEntity;
  }

  /**
   * 根据ID查找实体
   */
  async findById(id: string): Promise<T | null> {
    const db = dbManager.getDB();
    return await db.get<T>(this.storeName, id);
  }

  /**
   * 查找所有实体
   */
  async findAll(): Promise<T[]> {
    const db = dbManager.getDB();
    return await db.getAll<T>(this.storeName);
  }

  /**
   * 更新实体
   */
  async update(id: string, updates: Partial<Omit<T, 'id' | 'createdAt'>>): Promise<T> {
    const db = dbManager.getDB();
    
    const existing = await this.findById(id);
    if (!existing) {
      throw new Error(`实体不存在: ${id}`);
    }

    const updatedEntity: T = {
      ...existing,
      ...updates,
      updatedAt: new Date()
    };

    await db.put(this.storeName, updatedEntity);
    return updatedEntity;
  }

  /**
   * 删除实体
   */
  async delete(id: string): Promise<void> {
    const db = dbManager.getDB();
    await db.delete(this.storeName, id);
  }

  /**
   * 生成唯一ID
   */
  protected generateId(): string {
    return `${this.storeName}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 抽象方法：子类需要实现的查询方法
   */
  abstract findBy(criteria: Partial<T>): Promise<T[]>;
}