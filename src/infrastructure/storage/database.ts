import { globalDB, IndexedDBStorage } from '@/shared/utils/storage';

/**
 * 数据库初始化和管理
 */
export class DatabaseManager {
  private static instance: DatabaseManager;
  private db: IndexedDBStorage;
  private initialized = false;

  private constructor() {
    this.db = globalDB;
  }

  static getInstance(): DatabaseManager {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = new DatabaseManager();
    }
    return DatabaseManager.instance;
  }

  /**
   * 初始化数据库
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      await this.db.init();
      this.initialized = true;
      console.log('数据库初始化成功');
    } catch (error) {
      console.error('数据库初始化失败:', error);
      throw error;
    }
  }

  /**
   * 检查数据库是否已初始化
   */
  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * 获取数据库实例
   */
  getDB(): IndexedDBStorage {
    if (!this.initialized) {
      throw new Error('数据库未初始化，请先调用 initialize()');
    }
    return this.db;
  }

  /**
   * 关闭数据库连接
   */
  close(): void {
    this.db.close();
    this.initialized = false;
  }

  /**
   * 清空所有数据
   */
  async clearAllData(): Promise<void> {
    if (!this.initialized) {
      throw new Error('数据库未初始化');
    }

    try {
      await Promise.all([
        this.db.clear('userProfiles'),
        this.db.clear('foodEntries'),
        this.db.clear('dailySummaries'),
        this.db.clear('settings')
      ]);
      console.log('所有数据已清空');
    } catch (error) {
      console.error('清空数据失败:', error);
      throw error;
    }
  }

  /**
   * 导出数据
   */
  async exportData(): Promise<any> {
    if (!this.initialized) {
      throw new Error('数据库未初始化');
    }

    try {
      const [userProfiles, foodEntries, dailySummaries, settings] = await Promise.all([
        this.db.getAll('userProfiles'),
        this.db.getAll('foodEntries'),
        this.db.getAll('dailySummaries'),
        this.db.getAll('settings')
      ]);

      return {
        version: '1.0',
        exportDate: new Date().toISOString(),
        data: {
          userProfiles,
          foodEntries,
          dailySummaries,
          settings
        }
      };
    } catch (error) {
      console.error('导出数据失败:', error);
      throw error;
    }
  }

  /**
   * 导入数据
   */
  async importData(data: any): Promise<void> {
    if (!this.initialized) {
      throw new Error('数据库未初始化');
    }

    try {
      // 验证数据格式
      if (!data.data || typeof data.data !== 'object') {
        throw new Error('数据格式无效');
      }

      const { userProfiles, foodEntries, dailySummaries, settings } = data.data;

      // 清空现有数据
      await this.clearAllData();

      // 导入新数据
      if (userProfiles && Array.isArray(userProfiles)) {
        for (const profile of userProfiles) {
          await this.db.put('userProfiles', profile);
        }
      }

      if (foodEntries && Array.isArray(foodEntries)) {
        for (const entry of foodEntries) {
          await this.db.put('foodEntries', entry);
        }
      }

      if (dailySummaries && Array.isArray(dailySummaries)) {
        for (const summary of dailySummaries) {
          await this.db.put('dailySummaries', summary);
        }
      }

      if (settings && Array.isArray(settings)) {
        for (const setting of settings) {
          await this.db.put('settings', setting);
        }
      }

      console.log('数据导入成功');
    } catch (error) {
      console.error('导入数据失败:', error);
      throw error;
    }
  }

  /**
   * 获取数据库统计信息
   */
  async getStats(): Promise<{
    userProfiles: number;
    foodEntries: number;
    dailySummaries: number;
    settings: number;
    totalSize: number;
  }> {
    if (!this.initialized) {
      throw new Error('数据库未初始化');
    }

    try {
      const [userProfiles, foodEntries, dailySummaries, settings] = await Promise.all([
        this.db.getAll('userProfiles'),
        this.db.getAll('foodEntries'),
        this.db.getAll('dailySummaries'),
        this.db.getAll('settings')
      ]);

      // 估算数据大小（字节）
      const totalSize = JSON.stringify({
        userProfiles,
        foodEntries,
        dailySummaries,
        settings
      }).length;

      return {
        userProfiles: userProfiles.length,
        foodEntries: foodEntries.length,
        dailySummaries: dailySummaries.length,
        settings: settings.length,
        totalSize
      };
    } catch (error) {
      console.error('获取统计信息失败:', error);
      throw error;
    }
  }
}

// 创建全局数据库管理器实例
export const dbManager = DatabaseManager.getInstance();

// 自动初始化数据库
export const initializeDatabase = async (): Promise<void> => {
  try {
    await dbManager.initialize();
  } catch (error) {
    console.error('数据库自动初始化失败:', error);
  }
};

// 在应用启动时初始化数据库
if (typeof window !== 'undefined') {
  initializeDatabase();
}