import type { BaseEntity, Gender } from './common';

// 活动水平类型（本地定义以避免导入问题）
export type ActivityLevel = 'sedentary' | 'light' | 'moderate' | 'active' | 'veryActive';

// 用户档案接口
export interface UserProfile extends BaseEntity {
  // 基本信息
  name?: string;
  email?: string;
  avatar?: string;
  
  // 身体数据
  height: number; // 身高(cm)
  weight: number; // 体重(kg)
  age: number;    // 年龄
  gender: Gender; // 性别
  
  // 目标设置
  targetWeight: number;     // 目标体重(kg)
  targetDays: number;       // 目标天数
  activityLevel: ActivityLevel; // 活动水平
  
  // 计算结果
  bmr: number;              // 基础代谢率
  tdee: number;             // 总日消耗
  dailyCalorieLimit: number; // 每日卡路里限额
  
  // 三餐分配比例
  mealRatios: {
    breakfast: number; // 早餐比例 (0-1)
    lunch: number;     // 午餐比例 (0-1)
    dinner: number;    // 晚餐比例 (0-1)
  };
  
  // 偏好设置
  preferences: UserPreferences;
}

// 用户偏好设置
export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: 'zh-CN' | 'en-US';
  notifications: {
    mealReminders: boolean;
    dailySummary: boolean;
    weeklyReport: boolean;
  };
  units: {
    weight: 'kg' | 'lb';
    height: 'cm' | 'ft';
  };
}

// 用户档案创建表单
export interface CreateUserProfileForm {
  height: number;
  weight: number;
  age: number;
  gender: Gender;
  targetWeight: number;
  targetDays: number;
  activityLevel: ActivityLevel;
  name?: string;
}

// 用户档案更新表单
export interface UpdateUserProfileForm extends Partial<CreateUserProfileForm> {
  mealRatios?: {
    breakfast: number;
    lunch: number;
    dinner: number;
  };
  preferences?: Partial<UserPreferences>;
}