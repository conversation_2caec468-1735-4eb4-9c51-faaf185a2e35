// API相关类型定义

// Gemini API请求
export interface GeminiAPIRequest {
  model: string;
  image: File | Blob;
  prompt: string;
}

// Gemini API响应
export interface GeminiAPIResponse {
  success: boolean;
  data?: {
    foods: {
      name: string;
      calories: number;
      weight: number;
      confidence: number;
      alternatives?: string[];
    }[];
  };
  error?: string;
  rawResponse?: any;
}

// API错误类型
export interface APIError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
}

// API配置
export interface APIConfig {
  geminiApiKey: string;
  geminiEndpoint: string;
  geminiModel: string;
  timeout: number;
  maxImageSize: number;
}

// 请求选项
export interface RequestOptions {
  timeout?: number;
  retries?: number;
  headers?: Record<string, string>;
  signal?: AbortSignal;
}