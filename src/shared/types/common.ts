// 通用类型定义

export interface BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginationParams {
  page: number;
  limit: number;
  offset?: number;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

export interface AsyncState<T = any> {
  data: T | null;
  loading: LoadingState;
  error: string | null;
}

// 设备类型
export type DeviceType = 'mobile' | 'tablet' | 'desktop';

// 主题类型
export type Theme = 'light' | 'dark' | 'system';

// 语言类型
export type Language = 'zh-CN' | 'en-US';

// 性别类型
export type Gender = 'male' | 'female';

// 活动水平
export type ActivityLevel = 'sedentary' | 'light' | 'moderate' | 'active' | 'veryActive';

// 用餐时间类型
export type MealType = 'breakfast' | 'lunch' | 'dinner' | 'snack';

// 日期范围
export interface DateRange {
  start: Date;
  end: Date;
}