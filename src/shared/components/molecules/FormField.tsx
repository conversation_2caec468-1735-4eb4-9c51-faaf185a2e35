import React from 'react';
import Input, { InputProps } from '../atoms/Input';
import { cn } from '@/shared/utils/format';

export interface FormFieldProps extends Omit<InputProps, 'label' | 'error'> {
  label: string;
  error?: string;
  required?: boolean;
  description?: string;
}

const FormField = React.forwardRef<HTMLInputElement, FormFieldProps>(
  ({ 
    label,
    error,
    required = false,
    description,
    className,
    ...props 
  }, ref) => {
    return (
      <div className={cn('space-y-1', className)}>
        <label className="block text-sm font-medium text-gray-700">
          {label}
          {required && <span className="text-danger-500 ml-1">*</span>}
        </label>
        
        {description && (
          <p className="text-sm text-gray-500">{description}</p>
        )}
        
        <Input
          ref={ref}
          error={error}
          fullWidth
          {...props}
        />
      </div>
    );
  }
);

FormField.displayName = 'FormField';

export default FormField;