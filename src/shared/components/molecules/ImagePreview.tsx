import React, { useState } from 'react';
import { Modal } from '@/shared/components';
import { 
  XMarkIcon, 
  MagnifyingGlassPlusIcon,
  MagnifyingGlassMinusIcon,
  ArrowsPointingOutIcon
} from '@heroicons/react/24/outline';
import { cn } from '@/shared/utils/format';

interface ImagePreviewProps {
  src: string;
  alt?: string;
  className?: string;
  showFullscreen?: boolean;
  onDelete?: () => void;
}

const ImagePreview: React.FC<ImagePreviewProps> = ({
  src,
  alt = '图片预览',
  className,
  showFullscreen = true,
  onDelete
}) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [scale, setScale] = useState(1);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });

  // 缩放控制
  const zoomIn = () => {
    setScale(prev => Math.min(prev * 1.2, 3));
  };

  const zoomOut = () => {
    setScale(prev => Math.max(prev / 1.2, 0.5));
  };

  const resetZoom = () => {
    setScale(1);
    setPosition({ x: 0, y: 0 });
  };

  // 拖拽控制
  const handleMouseDown = (e: React.MouseEvent) => {
    if (scale > 1) {
      setIsDragging(true);
      setDragStart({
        x: e.clientX - position.x,
        y: e.clientY - position.y
      });
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging && scale > 1) {
      setPosition({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y
      });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // 触摸控制
  const handleTouchStart = (e: React.TouchEvent) => {
    if (e.touches.length === 1 && scale > 1) {
      const touch = e.touches[0];
      setIsDragging(true);
      setDragStart({
        x: touch.clientX - position.x,
        y: touch.clientY - position.y
      });
    }
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (isDragging && e.touches.length === 1 && scale > 1) {
      const touch = e.touches[0];
      setPosition({
        x: touch.clientX - dragStart.x,
        y: touch.clientY - dragStart.y
      });
    }
  };

  const handleTouchEnd = () => {
    setIsDragging(false);
  };

  // 关闭全屏
  const closeFullscreen = () => {
    setIsFullscreen(false);
    resetZoom();
  };

  return (
    <>
      {/* 缩略图 */}
      <div className={cn('relative group', className)}>
        <img
          src={src}
          alt={alt}
          className="w-full h-full object-cover rounded-lg cursor-pointer"
          onClick={() => showFullscreen && setIsFullscreen(true)}
        />
        
        {/* 悬浮操作按钮 */}
        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center">
          <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex gap-2">
            {showFullscreen && (
              <button
                onClick={() => setIsFullscreen(true)}
                className="p-2 bg-white bg-opacity-90 rounded-full text-gray-700 hover:bg-opacity-100 transition-all"
              >
                <ArrowsPointingOutIcon className="h-5 w-5" />
              </button>
            )}
            
            {onDelete && (
              <button
                onClick={onDelete}
                className="p-2 bg-red-500 bg-opacity-90 rounded-full text-white hover:bg-opacity-100 transition-all"
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            )}
          </div>
        </div>
      </div>

      {/* 全屏模态框 */}
      {showFullscreen && (
        <Modal
          isOpen={isFullscreen}
          onClose={closeFullscreen}
          size="full"
          showCloseButton={false}
        >
          <div className="relative h-full bg-black flex flex-col">
            {/* 顶部工具栏 */}
            <div className="absolute top-0 left-0 right-0 z-10 bg-gradient-to-b from-black to-transparent p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <button
                    onClick={zoomOut}
                    disabled={scale <= 0.5}
                    className="p-2 bg-black bg-opacity-50 rounded-full text-white disabled:opacity-50"
                  >
                    <MagnifyingGlassMinusIcon className="h-5 w-5" />
                  </button>
                  
                  <span className="text-white text-sm bg-black bg-opacity-50 px-3 py-1 rounded-full">
                    {Math.round(scale * 100)}%
                  </span>
                  
                  <button
                    onClick={zoomIn}
                    disabled={scale >= 3}
                    className="p-2 bg-black bg-opacity-50 rounded-full text-white disabled:opacity-50"
                  >
                    <MagnifyingGlassPlusIcon className="h-5 w-5" />
                  </button>
                  
                  <button
                    onClick={resetZoom}
                    className="text-white text-sm bg-black bg-opacity-50 px-3 py-1 rounded-full hover:bg-opacity-70"
                  >
                    重置
                  </button>
                </div>
                
                <button
                  onClick={closeFullscreen}
                  className="p-2 bg-black bg-opacity-50 rounded-full text-white"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>
            </div>

            {/* 图片容器 */}
            <div 
              className="flex-1 flex items-center justify-center overflow-hidden cursor-grab active:cursor-grabbing"
              onMouseDown={handleMouseDown}
              onMouseMove={handleMouseMove}
              onMouseUp={handleMouseUp}
              onMouseLeave={handleMouseUp}
              onTouchStart={handleTouchStart}
              onTouchMove={handleTouchMove}
              onTouchEnd={handleTouchEnd}
            >
              <img
                src={src}
                alt={alt}
                className="max-w-full max-h-full object-contain transition-transform duration-200 select-none"
                style={{
                  transform: `scale(${scale}) translate(${position.x / scale}px, ${position.y / scale}px)`,
                  cursor: scale > 1 ? (isDragging ? 'grabbing' : 'grab') : 'default'
                }}
                draggable={false}
              />
            </div>

            {/* 底部提示 */}
            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4">
              <p className="text-white text-sm text-center opacity-70">
                {scale > 1 ? '拖拽移动图片 • ' : ''}双击缩放 • 滑动手势缩放
              </p>
            </div>
          </div>
        </Modal>
      )}
    </>
  );
};

export default ImagePreview;