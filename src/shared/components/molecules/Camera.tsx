import React, { useRef, useState, useCallback } from 'react';
import { <PERSON><PERSON>, But<PERSON> } from '@/shared/components';
import { 
  CameraIcon, 
  PhotoIcon, 
  ArrowPathIcon,
  XMarkIcon,
  CheckIcon
} from '@heroicons/react/24/outline';
import { smartCompressImage } from '@/shared/utils/image';
import { cn } from '@/shared/utils/format';

interface CameraProps {
  isOpen: boolean;
  onClose: () => void;
  onCapture: (file: File) => void;
  maxSizeMB?: number;
}

const Camera: React.FC<CameraProps> = ({
  isOpen,
  onClose,
  onCapture,
  maxSizeMB = 2
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [facingMode, setFacingMode] = useState<'user' | 'environment'>('environment');

  // 启动相机
  const startCamera = useCallback(async () => {
    try {
      setError(null);
      setIsLoading(true);

      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode,
          width: { ideal: 1920 },
          height: { ideal: 1080 }
        }
      });

      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
        setStream(mediaStream);
      }
    } catch (err) {
      console.error('启动相机失败:', err);
      setError('无法访问相机，请检查权限设置');
    } finally {
      setIsLoading(false);
    }
  }, [facingMode]);

  // 停止相机
  const stopCamera = useCallback(() => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      setStream(null);
    }
  }, [stream]);

  // 拍照
  const takePhoto = useCallback(() => {
    if (!videoRef.current || !canvasRef.current) return;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');

    if (!context) return;

    // 设置canvas尺寸
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    // 绘制视频帧到canvas
    context.drawImage(video, 0, 0);

    // 转换为base64
    const imageData = canvas.toDataURL('image/jpeg', 0.8);
    setCapturedImage(imageData);
    stopCamera();
  }, [stopCamera]);

  // 重新拍照
  const retakePhoto = useCallback(() => {
    setCapturedImage(null);
    startCamera();
  }, [startCamera]);

  // 确认使用照片
  const confirmPhoto = useCallback(async () => {
    if (!capturedImage) return;

    try {
      setIsLoading(true);

      // 将base64转换为File对象
      const response = await fetch(capturedImage);
      const blob = await response.blob();
      const file = new File([blob], `photo_${Date.now()}.jpg`, {
        type: 'image/jpeg',
        lastModified: Date.now()
      });

      // 压缩图片
      const compressedFile = await smartCompressImage(file, maxSizeMB);
      
      onCapture(compressedFile);
      handleClose();
    } catch (err) {
      console.error('处理照片失败:', err);
      setError('照片处理失败，请重试');
    } finally {
      setIsLoading(false);
    }
  }, [capturedImage, maxSizeMB, onCapture]);

  // 从相册选择
  const selectFromGallery = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  // 处理文件选择
  const handleFileSelect = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      setIsLoading(true);

      // 验证文件类型
      if (!file.type.startsWith('image/')) {
        setError('请选择图片文件');
        return;
      }

      // 压缩图片
      const compressedFile = await smartCompressImage(file, maxSizeMB);
      
      onCapture(compressedFile);
      handleClose();
    } catch (err) {
      console.error('处理图片失败:', err);
      setError('图片处理失败，请重试');
    } finally {
      setIsLoading(false);
    }
  }, [maxSizeMB, onCapture]);

  // 切换摄像头
  const switchCamera = useCallback(() => {
    setFacingMode(prev => prev === 'user' ? 'environment' : 'user');
    if (stream) {
      stopCamera();
      // 延迟启动新摄像头
      setTimeout(startCamera, 100);
    }
  }, [stream, stopCamera, startCamera]);

  // 关闭相机
  const handleClose = useCallback(() => {
    stopCamera();
    setCapturedImage(null);
    setError(null);
    onClose();
  }, [stopCamera, onClose]);

  // 当模态框打开时启动相机
  React.useEffect(() => {
    if (isOpen && !capturedImage) {
      startCamera();
    }
    
    return () => {
      if (!isOpen) {
        stopCamera();
      }
    };
  }, [isOpen, capturedImage, startCamera, stopCamera]);

  return (
    <>
      <Modal
        isOpen={isOpen}
        onClose={handleClose}
        size="full"
        showCloseButton={false}
      >
        <div className="relative h-full bg-black">
          {/* 错误提示 */}
          {error && (
            <div className="absolute top-4 left-4 right-4 z-10 bg-red-500 text-white p-3 rounded-lg">
              {error}
            </div>
          )}

          {/* 加载状态 */}
          {isLoading && (
            <div className="absolute inset-0 z-20 bg-black bg-opacity-50 flex items-center justify-center">
              <div className="bg-white rounded-lg p-4 flex items-center gap-3">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"></div>
                <span>处理中...</span>
              </div>
            </div>
          )}

          {capturedImage ? (
            // 照片预览
            <div className="relative h-full flex flex-col">
              <img
                src={capturedImage}
                alt="拍摄的照片"
                className="flex-1 object-contain"
              />
              
              {/* 预览操作按钮 */}
              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-6">
                <div className="flex justify-center gap-4">
                  <Button
                    variant="secondary"
                    onClick={retakePhoto}
                    className="flex items-center gap-2"
                  >
                    <ArrowPathIcon className="h-5 w-5" />
                    重拍
                  </Button>
                  <Button
                    variant="primary"
                    onClick={confirmPhoto}
                    disabled={isLoading}
                    className="flex items-center gap-2"
                  >
                    <CheckIcon className="h-5 w-5" />
                    使用照片
                  </Button>
                </div>
              </div>
            </div>
          ) : (
            // 相机视图
            <div className="relative h-full flex flex-col">
              <video
                ref={videoRef}
                autoPlay
                playsInline
                muted
                className="flex-1 object-cover w-full"
              />
              
              {/* 相机控制按钮 */}
              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-6">
                <div className="flex items-center justify-between">
                  {/* 关闭按钮 */}
                  <button
                    onClick={handleClose}
                    className="p-3 bg-black bg-opacity-50 rounded-full text-white"
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>

                  {/* 拍照按钮 */}
                  <button
                    onClick={takePhoto}
                    disabled={!stream || isLoading}
                    className={cn(
                      "w-16 h-16 rounded-full border-4 border-white bg-white",
                      "disabled:opacity-50 disabled:cursor-not-allowed",
                      "active:scale-95 transition-transform"
                    )}
                  >
                    <CameraIcon className="h-8 w-8 text-gray-800 mx-auto" />
                  </button>

                  {/* 切换摄像头按钮 */}
                  <button
                    onClick={switchCamera}
                    disabled={!stream || isLoading}
                    className="p-3 bg-black bg-opacity-50 rounded-full text-white disabled:opacity-50"
                  >
                    <ArrowPathIcon className="h-6 w-6" />
                  </button>
                </div>

                {/* 相册选择按钮 */}
                <div className="flex justify-center mt-4">
                  <button
                    onClick={selectFromGallery}
                    className="flex items-center gap-2 px-4 py-2 bg-black bg-opacity-50 rounded-full text-white"
                  >
                    <PhotoIcon className="h-5 w-5" />
                    从相册选择
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* 隐藏的canvas用于拍照 */}
          <canvas ref={canvasRef} className="hidden" />
        </div>
      </Modal>

      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        className="hidden"
      />
    </>
  );
};

export default Camera;