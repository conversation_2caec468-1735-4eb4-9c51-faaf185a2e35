// 数据验证工具函数

export interface ValidationResult {
  isValid: boolean;
  message?: string;
}

/**
 * 验证身高
 */
export function validateHeight(height: number): ValidationResult {
  if (!height || isNaN(height)) {
    return { isValid: false, message: '请输入有效的身高' };
  }
  
  if (height < 100) {
    return { isValid: false, message: '身高不能小于100cm' };
  }
  
  if (height > 250) {
    return { isValid: false, message: '身高不能大于250cm' };
  }
  
  return { isValid: true };
}

/**
 * 验证体重
 */
export function validateWeight(weight: number): ValidationResult {
  if (!weight || isNaN(weight)) {
    return { isValid: false, message: '请输入有效的体重' };
  }
  
  if (weight < 30) {
    return { isValid: false, message: '体重不能小于30kg' };
  }
  
  if (weight > 300) {
    return { isValid: false, message: '体重不能大于300kg' };
  }
  
  return { isValid: true };
}

/**
 * 验证年龄
 */
export function validateAge(age: number): ValidationResult {
  if (!age || isNaN(age)) {
    return { isValid: false, message: '请输入有效的年龄' };
  }
  
  if (age < 10) {
    return { isValid: false, message: '年龄不能小于10岁' };
  }
  
  if (age > 100) {
    return { isValid: false, message: '年龄不能大于100岁' };
  }
  
  return { isValid: true };
}

/**
 * 验证邮箱格式
 */
export function validateEmail(email: string): ValidationResult {
  if (!email) {
    return { isValid: false, message: '请输入邮箱地址' };
  }
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return { isValid: false, message: '请输入有效的邮箱地址' };
  }
  
  return { isValid: true };
}

/**
 * 验证手机号格式
 */
export function validatePhoneNumber(phone: string): ValidationResult {
  if (!phone) {
    return { isValid: false, message: '请输入手机号' };
  }
  
  const phoneRegex = /^1[3-9]\d{9}$/;
  if (!phoneRegex.test(phone)) {
    return { isValid: false, message: '请输入有效的手机号' };
  }
  
  return { isValid: true };
}

/**
 * 验证密码强度
 */
export function validatePassword(password: string): ValidationResult {
  if (!password) {
    return { isValid: false, message: '请输入密码' };
  }
  
  if (password.length < 6) {
    return { isValid: false, message: '密码长度不能少于6位' };
  }
  
  if (password.length > 20) {
    return { isValid: false, message: '密码长度不能超过20位' };
  }
  
  // 检查是否包含字母和数字
  const hasLetter = /[a-zA-Z]/.test(password);
  const hasNumber = /\d/.test(password);
  
  if (!hasLetter || !hasNumber) {
    return { isValid: false, message: '密码必须包含字母和数字' };
  }
  
  return { isValid: true };
}

/**
 * 验证卡路里数值
 */
export function validateCalories(calories: number): ValidationResult {
  if (!calories || isNaN(calories)) {
    return { isValid: false, message: '请输入有效的卡路里数值' };
  }
  
  if (calories < 0) {
    return { isValid: false, message: '卡路里不能为负数' };
  }
  
  if (calories > 10000) {
    return { isValid: false, message: '卡路里数值过大' };
  }
  
  return { isValid: true };
}

/**
 * 验证食物重量
 */
export function validateFoodWeight(weight: number): ValidationResult {
  if (!weight || isNaN(weight)) {
    return { isValid: false, message: '请输入有效的重量' };
  }
  
  if (weight <= 0) {
    return { isValid: false, message: '重量必须大于0' };
  }
  
  if (weight > 5000) {
    return { isValid: false, message: '重量不能超过5000g' };
  }
  
  return { isValid: true };
}

/**
 * 验证文件大小
 */
export function validateFileSize(file: File, maxSizeMB: number = 2): ValidationResult {
  if (!file) {
    return { isValid: false, message: '请选择文件' };
  }
  
  const maxSizeBytes = maxSizeMB * 1024 * 1024;
  if (file.size > maxSizeBytes) {
    return { isValid: false, message: `文件大小不能超过${maxSizeMB}MB` };
  }
  
  return { isValid: true };
}

/**
 * 验证图片文件类型
 */
export function validateImageFile(file: File): ValidationResult {
  if (!file) {
    return { isValid: false, message: '请选择图片文件' };
  }
  
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  if (!allowedTypes.includes(file.type)) {
    return { isValid: false, message: '只支持 JPEG、PNG、WebP 格式的图片' };
  }
  
  return { isValid: true };
}

/**
 * 验证必填字段
 */
export function validateRequired(value: any, fieldName: string): ValidationResult {
  if (value === null || value === undefined || value === '') {
    return { isValid: false, message: `${fieldName}不能为空` };
  }
  
  return { isValid: true };
}

/**
 * 验证数值范围
 */
export function validateRange(
  value: number, 
  min: number, 
  max: number, 
  fieldName: string
): ValidationResult {
  if (!value || isNaN(value)) {
    return { isValid: false, message: `请输入有效的${fieldName}` };
  }
  
  if (value < min) {
    return { isValid: false, message: `${fieldName}不能小于${min}` };
  }
  
  if (value > max) {
    return { isValid: false, message: `${fieldName}不能大于${max}` };
  }
  
  return { isValid: true };
}

/**
 * 批量验证
 */
export function validateMultiple(validations: ValidationResult[]): ValidationResult {
  for (const validation of validations) {
    if (!validation.isValid) {
      return validation;
    }
  }
  
  return { isValid: true };
}