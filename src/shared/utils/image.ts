// 图片处理工具函数

/**
 * 压缩图片
 */
export function compressImage(
  file: File,
  maxWidth: number = 800,
  maxHeight: number = 600,
  quality: number = 0.8
): Promise<Blob> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // 计算新的尺寸
      let { width, height } = img;
      
      if (width > height) {
        if (width > maxWidth) {
          height = (height * maxWidth) / width;
          width = maxWidth;
        }
      } else {
        if (height > maxHeight) {
          width = (width * maxHeight) / height;
          height = maxHeight;
        }
      }

      // 设置canvas尺寸
      canvas.width = width;
      canvas.height = height;

      // 绘制图片
      ctx?.drawImage(img, 0, 0, width, height);

      // 转换为Blob
      canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('图片压缩失败'));
          }
        },
        'image/jpeg',
        quality
      );
    };

    img.onerror = () => {
      reject(new Error('图片加载失败'));
    };

    img.src = URL.createObjectURL(file);
  });
}

/**
 * 将文件转换为Base64
 */
export function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = () => {
      if (typeof reader.result === 'string') {
        resolve(reader.result);
      } else {
        reject(new Error('文件读取失败'));
      }
    };
    
    reader.onerror = () => {
      reject(new Error('文件读取失败'));
    };
    
    reader.readAsDataURL(file);
  });
}

/**
 * 将Base64转换为Blob
 */
export function base64ToBlob(base64: string, mimeType: string = 'image/jpeg'): Blob {
  const byteCharacters = atob(base64.split(',')[1]);
  const byteNumbers = new Array(byteCharacters.length);
  
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }
  
  const byteArray = new Uint8Array(byteNumbers);
  return new Blob([byteArray], { type: mimeType });
}

/**
 * 获取图片尺寸
 */
export function getImageDimensions(file: File): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    
    img.onload = () => {
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight
      });
    };
    
    img.onerror = () => {
      reject(new Error('无法获取图片尺寸'));
    };
    
    img.src = URL.createObjectURL(file);
  });
}

/**
 * 创建图片缩略图
 */
export function createThumbnail(
  file: File,
  size: number = 150
): Promise<string> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // 设置canvas为正方形
      canvas.width = size;
      canvas.height = size;

      // 计算裁剪区域（居中裁剪）
      const { width, height } = img;
      const minDimension = Math.min(width, height);
      const x = (width - minDimension) / 2;
      const y = (height - minDimension) / 2;

      // 绘制裁剪后的图片
      ctx?.drawImage(
        img,
        x, y, minDimension, minDimension,
        0, 0, size, size
      );

      // 转换为Base64
      const dataURL = canvas.toDataURL('image/jpeg', 0.8);
      resolve(dataURL);
    };

    img.onerror = () => {
      reject(new Error('缩略图生成失败'));
    };

    img.src = URL.createObjectURL(file);
  });
}

/**
 * 旋转图片
 */
export function rotateImage(file: File, degrees: number): Promise<Blob> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      const { width, height } = img;
      
      // 根据旋转角度设置canvas尺寸
      if (degrees === 90 || degrees === 270) {
        canvas.width = height;
        canvas.height = width;
      } else {
        canvas.width = width;
        canvas.height = height;
      }

      // 移动到中心点
      ctx?.translate(canvas.width / 2, canvas.height / 2);
      
      // 旋转
      ctx?.rotate((degrees * Math.PI) / 180);
      
      // 绘制图片
      ctx?.drawImage(img, -width / 2, -height / 2);

      // 转换为Blob
      canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('图片旋转失败'));
          }
        },
        'image/jpeg',
        0.9
      );
    };

    img.onerror = () => {
      reject(new Error('图片加载失败'));
    };

    img.src = URL.createObjectURL(file);
  });
}

/**
 * 检查图片是否需要压缩
 */
export function shouldCompressImage(
  file: File,
  maxSizeMB: number = 2,
  maxWidth: number = 1920,
  maxHeight: number = 1080
): Promise<boolean> {
  return new Promise(async (resolve) => {
    try {
      // 检查文件大小
      const fileSizeMB = file.size / (1024 * 1024);
      if (fileSizeMB > maxSizeMB) {
        resolve(true);
        return;
      }

      // 检查图片尺寸
      const dimensions = await getImageDimensions(file);
      if (dimensions.width > maxWidth || dimensions.height > maxHeight) {
        resolve(true);
        return;
      }

      resolve(false);
    } catch (error) {
      // 如果无法获取尺寸，默认需要压缩
      resolve(true);
    }
  });
}

/**
 * 智能压缩图片
 */
export async function smartCompressImage(
  file: File,
  maxSizeMB: number = 2
): Promise<File> {
  try {
    const needsCompression = await shouldCompressImage(file, maxSizeMB);
    
    if (!needsCompression) {
      return file;
    }

    // 根据原始大小调整压缩参数
    const fileSizeMB = file.size / (1024 * 1024);
    let quality = 0.8;
    let maxWidth = 1920;
    let maxHeight = 1080;

    if (fileSizeMB > 10) {
      quality = 0.6;
      maxWidth = 1200;
      maxHeight = 900;
    } else if (fileSizeMB > 5) {
      quality = 0.7;
      maxWidth = 1600;
      maxHeight = 1200;
    }

    const compressedBlob = await compressImage(file, maxWidth, maxHeight, quality);
    
    // 转换回File对象
    return new File([compressedBlob], file.name, {
      type: 'image/jpeg',
      lastModified: Date.now()
    });
  } catch (error) {
    console.error('图片压缩失败:', error);
    return file;
  }
}

/**
 * 批量处理图片
 */
export async function processImages(
  files: FileList | File[],
  options: {
    maxSizeMB?: number;
    maxWidth?: number;
    maxHeight?: number;
    quality?: number;
  } = {}
): Promise<File[]> {
  const {
    maxSizeMB = 2,
    maxWidth = 1920,
    maxHeight = 1080,
    quality = 0.8
  } = options;

  const fileArray = Array.from(files);
  const processedFiles: File[] = [];

  for (const file of fileArray) {
    try {
      const needsCompression = await shouldCompressImage(file, maxSizeMB, maxWidth, maxHeight);
      
      if (needsCompression) {
        const compressedBlob = await compressImage(file, maxWidth, maxHeight, quality);
        const processedFile = new File([compressedBlob], file.name, {
          type: 'image/jpeg',
          lastModified: Date.now()
        });
        processedFiles.push(processedFile);
      } else {
        processedFiles.push(file);
      }
    } catch (error) {
      console.error(`处理图片 ${file.name} 失败:`, error);
      processedFiles.push(file);
    }
  }

  return processedFiles;
}