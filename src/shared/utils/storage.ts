// 本地存储工具函数

/**
 * 安全的localStorage操作
 */
export class LocalStorage {
  /**
   * 设置数据
   */
  static set<T>(key: string, value: T): boolean {
    try {
      const serializedValue = JSON.stringify(value);
      localStorage.setItem(key, serializedValue);
      return true;
    } catch (error) {
      console.error('LocalStorage set error:', error);
      return false;
    }
  }

  /**
   * 获取数据
   */
  static get<T>(key: string, defaultValue?: T): T | null {
    try {
      const item = localStorage.getItem(key);
      if (item === null) {
        return defaultValue || null;
      }
      return JSON.parse(item) as T;
    } catch (error) {
      console.error('LocalStorage get error:', error);
      return defaultValue || null;
    }
  }

  /**
   * 删除数据
   */
  static remove(key: string): boolean {
    try {
      localStorage.removeItem(key);
      return true;
    } catch (error) {
      console.error('LocalStorage remove error:', error);
      return false;
    }
  }

  /**
   * 清空所有数据
   */
  static clear(): boolean {
    try {
      localStorage.clear();
      return true;
    } catch (error) {
      console.error('LocalStorage clear error:', error);
      return false;
    }
  }

  /**
   * 检查key是否存在
   */
  static has(key: string): boolean {
    return localStorage.getItem(key) !== null;
  }

  /**
   * 获取所有keys
   */
  static keys(): string[] {
    return Object.keys(localStorage);
  }

  /**
   * 获取存储大小（字节）
   */
  static getSize(): number {
    let total = 0;
    for (const key in localStorage) {
      if (localStorage.hasOwnProperty(key)) {
        total += localStorage[key].length + key.length;
      }
    }
    return total;
  }
}

/**
 * 安全的sessionStorage操作
 */
export class SessionStorage {
  /**
   * 设置数据
   */
  static set<T>(key: string, value: T): boolean {
    try {
      const serializedValue = JSON.stringify(value);
      sessionStorage.setItem(key, serializedValue);
      return true;
    } catch (error) {
      console.error('SessionStorage set error:', error);
      return false;
    }
  }

  /**
   * 获取数据
   */
  static get<T>(key: string, defaultValue?: T): T | null {
    try {
      const item = sessionStorage.getItem(key);
      if (item === null) {
        return defaultValue || null;
      }
      return JSON.parse(item) as T;
    } catch (error) {
      console.error('SessionStorage get error:', error);
      return defaultValue || null;
    }
  }

  /**
   * 删除数据
   */
  static remove(key: string): boolean {
    try {
      sessionStorage.removeItem(key);
      return true;
    } catch (error) {
      console.error('SessionStorage remove error:', error);
      return false;
    }
  }

  /**
   * 清空所有数据
   */
  static clear(): boolean {
    try {
      sessionStorage.clear();
      return true;
    } catch (error) {
      console.error('SessionStorage clear error:', error);
      return false;
    }
  }

  /**
   * 检查key是否存在
   */
  static has(key: string): boolean {
    return sessionStorage.getItem(key) !== null;
  }
}

/**
 * IndexedDB操作类
 */
export class IndexedDBStorage {
  private dbName: string;
  private version: number;
  private db: IDBDatabase | null = null;

  constructor(dbName: string = 'KCalTrackerDB', version: number = 1) {
    this.dbName = dbName;
    this.version = version;
  }

  /**
   * 初始化数据库
   */
  async init(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version);

      request.onerror = () => {
        reject(new Error('IndexedDB初始化失败'));
      };

      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        
        // 创建对象存储
        if (!db.objectStoreNames.contains('userProfiles')) {
          db.createObjectStore('userProfiles', { keyPath: 'id' });
        }
        
        if (!db.objectStoreNames.contains('foodEntries')) {
          const foodStore = db.createObjectStore('foodEntries', { keyPath: 'id' });
          foodStore.createIndex('date', 'consumedAt', { unique: false });
          foodStore.createIndex('mealType', 'mealType', { unique: false });
        }
        
        if (!db.objectStoreNames.contains('dailySummaries')) {
          const summaryStore = db.createObjectStore('dailySummaries', { keyPath: 'id' });
          summaryStore.createIndex('date', 'date', { unique: true });
        }
        
        if (!db.objectStoreNames.contains('settings')) {
          db.createObjectStore('settings', { keyPath: 'key' });
        }
      };
    });
  }

  /**
   * 添加数据
   */
  async add(storeName: string, data: any): Promise<void> {
    if (!this.db) {
      throw new Error('数据库未初始化');
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.add(data);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(new Error('添加数据失败'));
    });
  }

  /**
   * 更新数据
   */
  async put(storeName: string, data: any): Promise<void> {
    if (!this.db) {
      throw new Error('数据库未初始化');
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.put(data);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(new Error('更新数据失败'));
    });
  }

  /**
   * 获取数据
   */
  async get<T>(storeName: string, key: any): Promise<T | null> {
    if (!this.db) {
      throw new Error('数据库未初始化');
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.get(key);

      request.onsuccess = () => {
        resolve(request.result || null);
      };
      request.onerror = () => reject(new Error('获取数据失败'));
    });
  }

  /**
   * 获取所有数据
   */
  async getAll<T>(storeName: string): Promise<T[]> {
    if (!this.db) {
      throw new Error('数据库未初始化');
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.getAll();

      request.onsuccess = () => {
        resolve(request.result || []);
      };
      request.onerror = () => reject(new Error('获取数据失败'));
    });
  }

  /**
   * 删除数据
   */
  async delete(storeName: string, key: any): Promise<void> {
    if (!this.db) {
      throw new Error('数据库未初始化');
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.delete(key);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(new Error('删除数据失败'));
    });
  }

  /**
   * 清空存储
   */
  async clear(storeName: string): Promise<void> {
    if (!this.db) {
      throw new Error('数据库未初始化');
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.clear();

      request.onsuccess = () => resolve();
      request.onerror = () => reject(new Error('清空数据失败'));
    });
  }

  /**
   * 关闭数据库连接
   */
  close(): void {
    if (this.db) {
      this.db.close();
      this.db = null;
    }
  }
}

/**
 * 缓存管理器
 */
export class CacheManager {
  private static cache = new Map<string, { data: any; timestamp: number; ttl: number }>();

  /**
   * 设置缓存
   */
  static set<T>(key: string, data: T, ttlMinutes: number = 60): void {
    const ttl = ttlMinutes * 60 * 1000; // 转换为毫秒
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  /**
   * 获取缓存
   */
  static get<T>(key: string): T | null {
    const item = this.cache.get(key);
    
    if (!item) {
      return null;
    }

    // 检查是否过期
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }

    return item.data as T;
  }

  /**
   * 删除缓存
   */
  static delete(key: string): boolean {
    return this.cache.delete(key);
  }

  /**
   * 清空所有缓存
   */
  static clear(): void {
    this.cache.clear();
  }

  /**
   * 清理过期缓存
   */
  static cleanup(): void {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * 获取缓存大小
   */
  static size(): number {
    return this.cache.size;
  }
}

/**
 * 数据同步管理器
 */
export class DataSyncManager {
  private static syncQueue: Array<{ key: string; data: any; timestamp: number }> = [];

  /**
   * 添加到同步队列
   */
  static addToSyncQueue(key: string, data: any): void {
    this.syncQueue.push({
      key,
      data,
      timestamp: Date.now()
    });
  }

  /**
   * 获取同步队列
   */
  static getSyncQueue(): Array<{ key: string; data: any; timestamp: number }> {
    return [...this.syncQueue];
  }

  /**
   * 清空同步队列
   */
  static clearSyncQueue(): void {
    this.syncQueue = [];
  }

  /**
   * 移除已同步的项目
   */
  static removeSyncedItem(key: string, timestamp: number): void {
    this.syncQueue = this.syncQueue.filter(
      item => !(item.key === key && item.timestamp === timestamp)
    );
  }
}

// 创建全局IndexedDB实例
export const globalDB = new IndexedDBStorage();

// 定期清理缓存
setInterval(() => {
  CacheManager.cleanup();
}, 5 * 60 * 1000); // 每5分钟清理一次