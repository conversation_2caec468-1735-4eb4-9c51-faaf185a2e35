import React, { useState } from 'react';
import { <PERSON><PERSON>, FormField, Card, CardHeader, CardT<PERSON>le, CardContent } from '@/shared/components';
import { CreateUserProfileForm, Gender, ActivityLevel } from '@/shared/types';
import { validateWeightLossGoal } from '@/shared/utils';
import { cn } from '@/shared/utils/format';

interface ProfileSetupFormProps {
  onSubmit: (data: CreateUserProfileForm) => Promise<void>;
  loading?: boolean;
}

const ProfileSetupForm: React.FC<ProfileSetupFormProps> = ({ onSubmit, loading = false }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<Partial<CreateUserProfileForm>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});

  const totalSteps = 3;

  // 更新表单数据
  const updateFormData = (field: keyof CreateUserProfileForm, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // 清除该字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // 验证当前步骤
  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {};

    switch (step) {
      case 1:
        if (!formData.height || formData.height < 100 || formData.height > 250) {
          newErrors.height = '请输入有效的身高 (100-250cm)';
        }
        if (!formData.weight || formData.weight < 30 || formData.weight > 300) {
          newErrors.weight = '请输入有效的体重 (30-300kg)';
        }
        if (!formData.age || formData.age < 10 || formData.age > 100) {
          newErrors.age = '请输入有效的年龄 (10-100岁)';
        }
        if (!formData.gender) {
          newErrors.gender = '请选择性别';
        }
        break;

      case 2:
        if (!formData.targetWeight || formData.targetWeight < 30 || formData.targetWeight > 300) {
          newErrors.targetWeight = '请输入有效的目标体重 (30-300kg)';
        }
        if (!formData.targetDays || formData.targetDays < 7 || formData.targetDays > 365) {
          newErrors.targetDays = '请输入有效的目标天数 (7-365天)';
        }
        if (!formData.activityLevel) {
          newErrors.activityLevel = '请选择活动水平';
        }

        // 验证减重目标安全性
        if (formData.weight && formData.targetWeight && formData.targetDays) {
          const validation = validateWeightLossGoal(
            formData.weight,
            formData.targetWeight,
            formData.targetDays
          );
          if (!validation.isValid) {
            newErrors.targetWeight = validation.message || '减重目标不安全';
          }
        }
        break;

      case 3:
        // 第三步是确认步骤，无需额外验证
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 下一步
  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, totalSteps));
    }
  };

  // 上一步
  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  // 提交表单
  const handleSubmit = async () => {
    if (validateStep(currentStep)) {
      try {
        await onSubmit(formData as CreateUserProfileForm);
      } catch (error) {
        console.error('提交失败:', error);
      }
    }
  };

  // 活动水平选项
  const activityLevels: { value: ActivityLevel; label: string; description: string }[] = [
    { value: 'sedentary', label: '久坐不动', description: '很少或不运动' },
    { value: 'light', label: '轻度活动', description: '每周轻度运动1-3次' },
    { value: 'moderate', label: '中度活动', description: '每周中度运动3-5次' },
    { value: 'active', label: '高度活动', description: '每周高强度运动6-7次' },
    { value: 'veryActive', label: '极高活动', description: '每天高强度运动或体力劳动' }
  ];

  return (
    <div className="max-w-md mx-auto">
      {/* 进度指示器 */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          {Array.from({ length: totalSteps }, (_, i) => (
            <div
              key={i}
              className={cn(
                'w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium',
                i + 1 <= currentStep
                  ? 'bg-primary-600 text-white'
                  : 'bg-gray-200 text-gray-500'
              )}
            >
              {i + 1}
            </div>
          ))}
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-primary-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${(currentStep / totalSteps) * 100}%` }}
          />
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>
            {currentStep === 1 && '基本信息'}
            {currentStep === 2 && '目标设置'}
            {currentStep === 3 && '确认信息'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* 第一步：基本信息 */}
          {currentStep === 1 && (
            <div className="space-y-4">
              <FormField
                label="姓名"
                placeholder="请输入姓名（可选）"
                value={formData.name || ''}
                onChange={(e) => updateFormData('name', e.target.value)}
              />
              
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  label="身高"
                  type="number"
                  placeholder="170"
                  value={formData.height || ''}
                  onChange={(e) => updateFormData('height', Number(e.target.value))}
                  error={errors.height}
                  description="单位：厘米(cm)"
                  required
                />
                <FormField
                  label="体重"
                  type="number"
                  placeholder="65"
                  value={formData.weight || ''}
                  onChange={(e) => updateFormData('weight', Number(e.target.value))}
                  error={errors.weight}
                  description="单位：千克(kg)"
                  required
                />
              </div>

              <FormField
                label="年龄"
                type="number"
                placeholder="25"
                value={formData.age || ''}
                onChange={(e) => updateFormData('age', Number(e.target.value))}
                error={errors.age}
                description="单位：岁"
                required
              />

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  性别 <span className="text-danger-500">*</span>
                </label>
                <div className="grid grid-cols-2 gap-2">
                  {(['male', 'female'] as Gender[]).map((gender) => (
                    <button
                      key={gender}
                      type="button"
                      onClick={() => updateFormData('gender', gender)}
                      className={cn(
                        'p-3 rounded-lg border text-center transition-colors',
                        formData.gender === gender
                          ? 'border-primary-500 bg-primary-50 text-primary-700'
                          : 'border-gray-300 hover:border-gray-400'
                      )}
                    >
                      {gender === 'male' ? '男性' : '女性'}
                    </button>
                  ))}
                </div>
                {errors.gender && (
                  <p className="mt-1 text-sm text-danger-600">{errors.gender}</p>
                )}
              </div>
            </div>
          )}

          {/* 第二步：目标设置 */}
          {currentStep === 2 && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  label="目标体重"
                  type="number"
                  placeholder="60"
                  value={formData.targetWeight || ''}
                  onChange={(e) => updateFormData('targetWeight', Number(e.target.value))}
                  error={errors.targetWeight}
                  description="单位：千克(kg)"
                  required
                />
                <FormField
                  label="目标天数"
                  type="number"
                  placeholder="90"
                  value={formData.targetDays || ''}
                  onChange={(e) => updateFormData('targetDays', Number(e.target.value))}
                  error={errors.targetDays}
                  description="单位：天"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  活动水平 <span className="text-danger-500">*</span>
                </label>
                <div className="space-y-2">
                  {activityLevels.map((level) => (
                    <button
                      key={level.value}
                      type="button"
                      onClick={() => updateFormData('activityLevel', level.value)}
                      className={cn(
                        'w-full p-3 rounded-lg border text-left transition-colors',
                        formData.activityLevel === level.value
                          ? 'border-primary-500 bg-primary-50'
                          : 'border-gray-300 hover:border-gray-400'
                      )}
                    >
                      <div className="font-medium">{level.label}</div>
                      <div className="text-sm text-gray-500">{level.description}</div>
                    </button>
                  ))}
                </div>
                {errors.activityLevel && (
                  <p className="mt-1 text-sm text-danger-600">{errors.activityLevel}</p>
                )}
              </div>
            </div>
          )}

          {/* 第三步：确认信息 */}
          {currentStep === 3 && (
            <div className="space-y-4">
              <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                <h4 className="font-medium text-gray-900">基本信息</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">身高：</span>
                    <span className="font-medium">{formData.height} cm</span>
                  </div>
                  <div>
                    <span className="text-gray-500">体重：</span>
                    <span className="font-medium">{formData.weight} kg</span>
                  </div>
                  <div>
                    <span className="text-gray-500">年龄：</span>
                    <span className="font-medium">{formData.age} 岁</span>
                  </div>
                  <div>
                    <span className="text-gray-500">性别：</span>
                    <span className="font-medium">{formData.gender === 'male' ? '男性' : '女性'}</span>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                <h4 className="font-medium text-gray-900">目标设置</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">目标体重：</span>
                    <span className="font-medium">{formData.targetWeight} kg</span>
                  </div>
                  <div>
                    <span className="text-gray-500">目标天数：</span>
                    <span className="font-medium">{formData.targetDays} 天</span>
                  </div>
                  <div className="col-span-2">
                    <span className="text-gray-500">活动水平：</span>
                    <span className="font-medium">
                      {activityLevels.find(l => l.value === formData.activityLevel)?.label}
                    </span>
                  </div>
                </div>
              </div>

              <div className="bg-primary-50 rounded-lg p-4">
                <p className="text-sm text-primary-700">
                  点击"完成设置"后，系统将根据您的信息计算每日卡路里限额和营养建议。
                </p>
              </div>
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex justify-between mt-6">
            <Button
              variant="secondary"
              onClick={handlePrevious}
              disabled={currentStep === 1}
            >
              上一步
            </Button>

            {currentStep < totalSteps ? (
              <Button
                variant="primary"
                onClick={handleNext}
              >
                下一步
              </Button>
            ) : (
              <Button
                variant="primary"
                onClick={handleSubmit}
                loading={loading}
              >
                完成设置
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProfileSetupForm;