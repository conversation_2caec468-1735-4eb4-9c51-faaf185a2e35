import React, { useState } from 'react';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import { formatDate, getMonthDays, isInSamePeriod } from '@/shared/utils';
import { useNutritionStore } from '@/domains/nutrition/stores/nutritionStore';
import { cn } from '@/shared/utils/format';

interface CalendarProps {
  onDateSelect?: (date: Date) => void;
  selectedDate?: Date;
  className?: string;
}

const Calendar: React.FC<CalendarProps> = ({
  onDateSelect,
  selectedDate,
  className
}) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const { getDailySummary } = useNutritionStore();

  const year = currentDate.getFullYear();
  const month = currentDate.getMonth() + 1;
  const monthDays = getMonthDays(year, month);

  // 获取月份的第一天是星期几
  const firstDayOfMonth = new Date(year, month - 1, 1);
  const startDayOfWeek = firstDayOfMonth.getDay(); // 0 = Sunday

  // 生成日历网格（包含上个月的尾部日期）
  const calendarDays: (Date | null)[] = [];
  
  // 添加上个月的尾部日期
  for (let i = 0; i < startDayOfWeek; i++) {
    calendarDays.push(null);
  }
  
  // 添加当月的所有日期
  monthDays.forEach(day => {
    calendarDays.push(day);
  });

  // 导航到上个月
  const goToPreviousMonth = () => {
    const newDate = new Date(currentDate);
    newDate.setMonth(newDate.getMonth() - 1);
    setCurrentDate(newDate);
  };

  // 导航到下个月
  const goToNextMonth = () => {
    const newDate = new Date(currentDate);
    newDate.setMonth(newDate.getMonth() + 1);
    setCurrentDate(newDate);
  };

  // 获取日期的状态颜色
  const getDateStatus = (date: Date) => {
    const summary = getDailySummary(date);
    if (!summary || summary.totalCalories === 0) {
      return 'no-data';
    }

    const percentage = summary.percentage;
    if (percentage < 90) return 'under';
    if (percentage <= 110) return 'normal';
    if (percentage <= 130) return 'over';
    return 'exceed';
  };

  // 获取状态对应的样式
  const getStatusStyle = (status: string, isSelected: boolean, isToday: boolean) => {
    const baseClasses = 'w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-200';
    
    if (isSelected) {
      return cn(baseClasses, 'bg-primary-600 text-white ring-2 ring-primary-200');
    }

    if (isToday) {
      switch (status) {
        case 'under':
          return cn(baseClasses, 'bg-blue-100 text-blue-800 ring-2 ring-blue-300');
        case 'normal':
          return cn(baseClasses, 'bg-green-100 text-green-800 ring-2 ring-green-300');
        case 'over':
          return cn(baseClasses, 'bg-yellow-100 text-yellow-800 ring-2 ring-yellow-300');
        case 'exceed':
          return cn(baseClasses, 'bg-red-100 text-red-800 ring-2 ring-red-300');
        default:
          return cn(baseClasses, 'bg-gray-100 text-gray-800 ring-2 ring-gray-300');
      }
    }

    switch (status) {
      case 'under':
        return cn(baseClasses, 'bg-blue-100 text-blue-800 hover:bg-blue-200');
      case 'normal':
        return cn(baseClasses, 'bg-green-100 text-green-800 hover:bg-green-200');
      case 'over':
        return cn(baseClasses, 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200');
      case 'exceed':
        return cn(baseClasses, 'bg-red-100 text-red-800 hover:bg-red-200');
      default:
        return cn(baseClasses, 'text-gray-600 hover:bg-gray-100');
    }
  };

  const today = new Date();
  const weekDays = ['日', '一', '二', '三', '四', '五', '六'];

  return (
    <div className={cn('bg-white rounded-lg shadow-sm border border-gray-200', className)}>
      {/* 日历头部 */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <button
          onClick={goToPreviousMonth}
          className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <ChevronLeftIcon className="h-5 w-5 text-gray-600" />
        </button>
        
        <h3 className="text-lg font-semibold text-gray-900">
          {year}年{month}月
        </h3>
        
        <button
          onClick={goToNextMonth}
          className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <ChevronRightIcon className="h-5 w-5 text-gray-600" />
        </button>
      </div>

      {/* 星期标题 */}
      <div className="grid grid-cols-7 gap-1 p-4 pb-2">
        {weekDays.map(day => (
          <div key={day} className="text-center text-sm font-medium text-gray-500 py-2">
            {day}
          </div>
        ))}
      </div>

      {/* 日历网格 */}
      <div className="grid grid-cols-7 gap-1 p-4 pt-0">
        {calendarDays.map((date, index) => {
          if (!date) {
            return <div key={index} className="h-8" />;
          }

          const isToday = isInSamePeriod(date, today, 'day');
          const isSelected = selectedDate && isInSamePeriod(date, selectedDate, 'day');
          const status = getDateStatus(date);
          const dayNumber = date.getDate();

          return (
            <button
              key={index}
              onClick={() => onDateSelect?.(date)}
              className={getStatusStyle(status, !!isSelected, isToday)}
              title={`${formatDate(date, 'yyyy年MM月dd日')} - ${getStatusText(status)}`}
            >
              {dayNumber}
            </button>
          );
        })}
      </div>

      {/* 图例 */}
      <div className="px-4 pb-4">
        <div className="flex flex-wrap gap-3 text-xs">
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 rounded-full bg-green-100"></div>
            <span className="text-gray-600">达标</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 rounded-full bg-yellow-100"></div>
            <span className="text-gray-600">接近</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 rounded-full bg-red-100"></div>
            <span className="text-gray-600">超标</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 rounded-full bg-blue-100"></div>
            <span className="text-gray-600">未达标</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 rounded-full bg-gray-100"></div>
            <span className="text-gray-600">无数据</span>
          </div>
        </div>
      </div>
    </div>
  );
};

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'under':
      return '未达标';
    case 'normal':
      return '达标';
    case 'over':
      return '接近超标';
    case 'exceed':
      return '超标';
    default:
      return '无数据';
  }
};

export default Calendar;