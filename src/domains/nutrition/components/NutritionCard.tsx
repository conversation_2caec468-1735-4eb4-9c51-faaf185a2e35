import React from 'react';
import { Card, CardHeader, CardTitle, CardContent, Badge } from '@/shared/components';
import { DailySummary } from '@/shared/types';
import { formatCalories, formatPercentage, cn } from '@/shared/utils';

interface NutritionCardProps {
  summary: DailySummary;
  showDetails?: boolean;
}

const NutritionCard: React.FC<NutritionCardProps> = ({ 
  summary, 
  showDetails = true 
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'under':
        return 'text-blue-600 bg-blue-50';
      case 'normal':
        return 'text-green-600 bg-green-50';
      case 'over':
        return 'text-yellow-600 bg-yellow-50';
      case 'exceed':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'under':
        return '未达标';
      case 'normal':
        return '达标';
      case 'over':
        return '接近超标';
      case 'exceed':
        return '超标';
      default:
        return '无数据';
    }
  };

  const getProgressBarColor = (percentage: number) => {
    if (percentage < 90) return 'bg-blue-500';
    if (percentage <= 110) return 'bg-green-500';
    if (percentage <= 130) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>今日营养摄入</CardTitle>
          <Badge 
            variant={summary.status === 'normal' ? 'success' : 
                    summary.status === 'over' ? 'warning' : 
                    summary.status === 'exceed' ? 'danger' : 'default'}
          >
            {getStatusText(summary.status)}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* 卡路里总览 */}
          <div className="text-center">
            <div className="text-3xl font-bold text-gray-900 mb-1">
              {formatCalories(summary.totalCalories)}
            </div>
            <div className="text-sm text-gray-500">
              目标：{formatCalories(summary.calorieLimit)}
            </div>
            <div className="text-sm text-gray-500">
              剩余：{formatCalories(Math.max(0, summary.remainingCalories))}
            </div>
          </div>

          {/* 进度条 */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">完成度</span>
              <span className="font-medium">{formatPercentage(summary.percentage / 100)}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div
                className={cn(
                  'h-3 rounded-full transition-all duration-300',
                  getProgressBarColor(summary.percentage)
                )}
                style={{ width: `${Math.min(summary.percentage, 100)}%` }}
              />
            </div>
          </div>

          {/* 三餐分布 */}
          {showDetails && (
            <div className="space-y-3">
              <h4 className="font-medium text-gray-900">三餐分布</h4>
              <div className="space-y-2">
                {Object.entries(summary.mealBreakdown).map(([mealType, meal]) => {
                  if (mealType === 'snack' && meal.calories === 0) return null;
                  
                  const mealNames = {
                    breakfast: '早餐',
                    lunch: '午餐',
                    dinner: '晚餐',
                    snack: '零食'
                  };

                  return (
                    <div key={mealType} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-600">
                          {mealNames[mealType as keyof typeof mealNames]}
                        </span>
                        <Badge size="sm" variant="default">
                          {meal.foodCount}项
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium">
                          {formatCalories(meal.calories)}
                        </span>
                        <span className="text-xs text-gray-500">
                          / {formatCalories(meal.calorieLimit)}
                        </span>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {/* 营养素分布 */}
          {showDetails && (
            <div className="space-y-3">
              <h4 className="font-medium text-gray-900">营养素摄入</h4>
              <div className="grid grid-cols-2 gap-3">
                <div className="bg-gray-50 rounded-lg p-3 text-center">
                  <div className="text-lg font-semibold text-gray-900">
                    {summary.nutrition.protein.toFixed(1)}g
                  </div>
                  <div className="text-xs text-gray-500">蛋白质</div>
                </div>
                <div className="bg-gray-50 rounded-lg p-3 text-center">
                  <div className="text-lg font-semibold text-gray-900">
                    {summary.nutrition.carbs.toFixed(1)}g
                  </div>
                  <div className="text-xs text-gray-500">碳水化合物</div>
                </div>
                <div className="bg-gray-50 rounded-lg p-3 text-center">
                  <div className="text-lg font-semibold text-gray-900">
                    {summary.nutrition.fat.toFixed(1)}g
                  </div>
                  <div className="text-xs text-gray-500">脂肪</div>
                </div>
                <div className="bg-gray-50 rounded-lg p-3 text-center">
                  <div className="text-lg font-semibold text-gray-900">
                    {summary.nutrition.fiber.toFixed(1)}g
                  </div>
                  <div className="text-xs text-gray-500">膳食纤维</div>
                </div>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default NutritionCard;