import React from 'react';
import { Card, CardHeader, CardTitle, CardContent, Badge } from '@/shared/components';
import { DailySummary, UserProfile } from '@/shared/types';
import { formatCalories } from '@/shared/utils';
import { 
  LightBulbIcon, 
  ExclamationTriangleIcon, 
  CheckCircleIcon,
  InformationCircleIcon 
} from '@heroicons/react/24/outline';

interface NutritionAdviceProps {
  summary: DailySummary;
  profile: UserProfile;
}

interface Advice {
  type: 'success' | 'warning' | 'info' | 'tip';
  title: string;
  message: string;
  icon: React.ComponentType<{ className?: string }>;
}

const NutritionAdvice: React.FC<NutritionAdviceProps> = ({ summary, profile }) => {
  const generateAdvice = (): Advice[] => {
    const advice: Advice[] = [];
    const percentage = summary.percentage;
    const remaining = summary.remainingCalories;

    // 卡路里摄入建议
    if (percentage < 70) {
      advice.push({
        type: 'warning',
        title: '卡路里摄入不足',
        message: `您今天只摄入了目标的${Math.round(percentage)}%，建议增加健康食物的摄入。`,
        icon: ExclamationTriangleIcon
      });
    } else if (percentage >= 90 && percentage <= 110) {
      advice.push({
        type: 'success',
        title: '卡路里摄入理想',
        message: '您的卡路里摄入量非常理想，继续保持！',
        icon: CheckCircleIcon
      });
    } else if (percentage > 130) {
      advice.push({
        type: 'warning',
        title: '卡路里摄入过量',
        message: `您已超出目标${Math.round(percentage - 100)}%，建议明天适当减少摄入。`,
        icon: ExclamationTriangleIcon
      });
    }

    // 三餐分布建议
    const { breakfast, lunch, dinner } = summary.mealBreakdown;
    
    if (breakfast.calories === 0) {
      advice.push({
        type: 'tip',
        title: '别忘记早餐',
        message: '早餐是一天中最重要的一餐，建议摄入300-500卡路里。',
        icon: LightBulbIcon
      });
    }

    if (dinner.percentage > 150) {
      advice.push({
        type: 'info',
        title: '晚餐摄入较多',
        message: '晚餐摄入过多可能影响睡眠和消化，建议适当减少。',
        icon: InformationCircleIcon
      });
    }

    // 营养素建议
    const { protein, carbs, fat } = summary.nutrition;
    const totalMacros = protein + carbs + fat;
    
    if (totalMacros > 0) {
      const proteinPercentage = (protein / totalMacros) * 100;
      
      if (proteinPercentage < 15) {
        advice.push({
          type: 'tip',
          title: '增加蛋白质摄入',
          message: '蛋白质摄入偏低，建议增加瘦肉、鱼类、豆类等高蛋白食物。',
          icon: LightBulbIcon
        });
      }
    }

    // 基于用户目标的建议
    if (profile.targetWeight < profile.weight) {
      // 减重目标
      if (remaining > 0 && remaining < 200) {
        advice.push({
          type: 'success',
          title: '减重进展良好',
          message: `还剩${formatCalories(remaining)}，可以选择一些低卡零食。`,
          icon: CheckCircleIcon
        });
      }
    }

    // 水分摄入提醒
    advice.push({
      type: 'info',
      title: '记得补充水分',
      message: '建议每天饮水1.5-2升，有助于新陈代谢和减重。',
      icon: InformationCircleIcon
    });

    return advice;
  };

  const adviceList = generateAdvice();

  const getAdviceStyle = (type: string) => {
    switch (type) {
      case 'success':
        return 'border-green-200 bg-green-50';
      case 'warning':
        return 'border-yellow-200 bg-yellow-50';
      case 'info':
        return 'border-blue-200 bg-blue-50';
      case 'tip':
        return 'border-purple-200 bg-purple-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  const getIconStyle = (type: string) => {
    switch (type) {
      case 'success':
        return 'text-green-600';
      case 'warning':
        return 'text-yellow-600';
      case 'info':
        return 'text-blue-600';
      case 'tip':
        return 'text-purple-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>营养建议</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {adviceList.map((advice, index) => {
            const Icon = advice.icon;
            
            return (
              <div
                key={index}
                className={`border rounded-lg p-3 ${getAdviceStyle(advice.type)}`}
              >
                <div className="flex items-start gap-3">
                  <Icon className={`h-5 w-5 mt-0.5 flex-shrink-0 ${getIconStyle(advice.type)}`} />
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900 mb-1">
                      {advice.title}
                    </h4>
                    <p className="text-sm text-gray-700">
                      {advice.message}
                    </p>
                  </div>
                </div>
              </div>
            );
          })}
          
          {adviceList.length === 0 && (
            <div className="text-center py-4 text-gray-500">
              <InformationCircleIcon className="h-8 w-8 mx-auto mb-2 text-gray-400" />
              <p>暂无营养建议</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default NutritionAdvice;