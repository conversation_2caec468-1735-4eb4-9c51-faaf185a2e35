import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { DailySummary, NutritionAnalysis, MealSummary } from '@/shared/types';
import { formatDate, getDateRange } from '@/shared/utils';

interface NutritionState {
  // 状态
  dailySummaries: Record<string, DailySummary>; // 以日期为key的每日汇总
  currentDate: Date;
  loading: boolean;
  error: string | null;

  // 操作
  setCurrentDate: (date: Date) => void;
  getDailySummary: (date: Date) => DailySummary | null;
  updateDailySummary: (date: Date, summary: Partial<DailySummary>) => void;
  calculateMealSummary: (date: Date, mealType: string, calories: number, calorieLimit: number) => MealSummary;
  getWeeklyAnalysis: (startDate: Date) => NutritionAnalysis;
  getMonthlyAnalysis: (year: number, month: number) => NutritionAnalysis;
  clearData: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

export const useNutritionStore = create<NutritionState>()(
  persist(
    (set, get) => ({
      // 初始状态
      dailySummaries: {},
      currentDate: new Date(),
      loading: false,
      error: null,

      // 设置当前日期
      setCurrentDate: (date: Date) => {
        set({ currentDate: date });
      },

      // 获取每日汇总
      getDailySummary: (date: Date) => {
        const dateKey = formatDate(date, 'yyyy-MM-dd');
        return get().dailySummaries[dateKey] || null;
      },

      // 更新每日汇总
      updateDailySummary: (date: Date, summary: Partial<DailySummary>) => {
        const dateKey = formatDate(date, 'yyyy-MM-dd');
        const currentSummaries = get().dailySummaries;
        
        const existingSummary = currentSummaries[dateKey];
        const updatedSummary: DailySummary = {
          id: existingSummary?.id || `summary_${Date.now()}`,
          createdAt: existingSummary?.createdAt || new Date(),
          updatedAt: new Date(),
          date,
          totalCalories: 0,
          calorieLimit: 2000,
          remainingCalories: 2000,
          mealBreakdown: {
            breakfast: { mealType: 'breakfast', calories: 0, calorieLimit: 600, foodCount: 0, percentage: 0 },
            lunch: { mealType: 'lunch', calories: 0, calorieLimit: 800, foodCount: 0, percentage: 0 },
            dinner: { mealType: 'dinner', calories: 0, calorieLimit: 600, foodCount: 0, percentage: 0 },
            snack: { mealType: 'snack', calories: 0, calorieLimit: 0, foodCount: 0, percentage: 0 }
          },
          nutrition: {
            protein: 0,
            fat: 0,
            carbs: 0,
            fiber: 0,
            sugar: 0
          },
          status: 'under',
          percentage: 0,
          ...existingSummary,
          ...summary
        };

        // 重新计算状态和百分比
        const percentage = (updatedSummary.totalCalories / updatedSummary.calorieLimit) * 100;
        updatedSummary.percentage = percentage;
        updatedSummary.remainingCalories = updatedSummary.calorieLimit - updatedSummary.totalCalories;

        if (percentage < 90) {
          updatedSummary.status = 'under';
        } else if (percentage <= 110) {
          updatedSummary.status = 'normal';
        } else if (percentage <= 130) {
          updatedSummary.status = 'over';
        } else {
          updatedSummary.status = 'exceed';
        }

        set({
          dailySummaries: {
            ...currentSummaries,
            [dateKey]: updatedSummary
          }
        });
      },

      // 计算单餐汇总
      calculateMealSummary: (date: Date, mealType: string, calories: number, calorieLimit: number) => {
        const percentage = calorieLimit > 0 ? (calories / calorieLimit) * 100 : 0;
        
        return {
          mealType: mealType as any,
          calories,
          calorieLimit,
          foodCount: 1, // 这里简化处理，实际应该从食物条目计算
          percentage
        };
      },

      // 获取周度分析
      getWeeklyAnalysis: (startDate: Date) => {
        const { start, end } = getDateRange('week', startDate);
        const summaries = get().dailySummaries;
        
        const weekData: { date: Date; calories: number }[] = [];
        let totalCalories = 0;
        let totalTarget = 0;
        let daysWithData = 0;

        // 遍历一周的数据
        for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
          const dateKey = formatDate(d, 'yyyy-MM-dd');
          const summary = summaries[dateKey];
          
          if (summary) {
            weekData.push({
              date: new Date(d),
              calories: summary.totalCalories
            });
            totalCalories += summary.totalCalories;
            totalTarget += summary.calorieLimit;
            daysWithData++;
          } else {
            weekData.push({
              date: new Date(d),
              calories: 0
            });
          }
        }

        const averageCalories = daysWithData > 0 ? totalCalories / daysWithData : 0;
        const averageTarget = daysWithData > 0 ? totalTarget / daysWithData : 0;
        const adherenceRate = averageTarget > 0 ? (averageCalories / averageTarget) : 0;

        return {
          period: 'weekly',
          startDate: start,
          endDate: end,
          calories: {
            average: averageCalories,
            total: totalCalories,
            target: averageTarget,
            adherenceRate
          },
          nutrition: {
            protein: { average: 0, total: 0, recommended: 0, percentage: 0 },
            fat: { average: 0, total: 0, recommended: 0, percentage: 0 },
            carbs: { average: 0, total: 0, recommended: 0, percentage: 0 },
            fiber: { average: 0, total: 0, recommended: 0, percentage: 0 }
          },
          trends: weekData
        };
      },

      // 获取月度分析
      getMonthlyAnalysis: (year: number, month: number) => {
        const startDate = new Date(year, month - 1, 1);
        const endDate = new Date(year, month, 0);
        const summaries = get().dailySummaries;
        
        const monthData: { date: Date; calories: number }[] = [];
        let totalCalories = 0;
        let totalTarget = 0;
        let daysWithData = 0;

        // 遍历一个月的数据
        for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
          const dateKey = formatDate(d, 'yyyy-MM-dd');
          const summary = summaries[dateKey];
          
          if (summary) {
            monthData.push({
              date: new Date(d),
              calories: summary.totalCalories
            });
            totalCalories += summary.totalCalories;
            totalTarget += summary.calorieLimit;
            daysWithData++;
          } else {
            monthData.push({
              date: new Date(d),
              calories: 0
            });
          }
        }

        const averageCalories = daysWithData > 0 ? totalCalories / daysWithData : 0;
        const averageTarget = daysWithData > 0 ? totalTarget / daysWithData : 0;
        const adherenceRate = averageTarget > 0 ? (averageCalories / averageTarget) : 0;

        return {
          period: 'monthly',
          startDate,
          endDate,
          calories: {
            average: averageCalories,
            total: totalCalories,
            target: averageTarget,
            adherenceRate
          },
          nutrition: {
            protein: { average: 0, total: 0, recommended: 0, percentage: 0 },
            fat: { average: 0, total: 0, recommended: 0, percentage: 0 },
            carbs: { average: 0, total: 0, recommended: 0, percentage: 0 },
            fiber: { average: 0, total: 0, recommended: 0, percentage: 0 }
          },
          trends: monthData
        };
      },

      // 清除数据
      clearData: () => {
        set({
          dailySummaries: {},
          error: null
        });
      },

      // 设置加载状态
      setLoading: (loading: boolean) => {
        set({ loading });
      },

      // 设置错误信息
      setError: (error: string | null) => {
        set({ error });
      }
    }),
    {
      name: 'nutrition-data-storage',
      partialize: (state) => ({ 
        dailySummaries: state.dailySummaries 
      })
    }
  )
);