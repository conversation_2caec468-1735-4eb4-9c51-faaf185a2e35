import React from 'react';
import { createBrowserRouter, Navigate } from 'react-router-dom';
import { useUserStore } from '@/domains/user/stores/userStore';
import ProfileSetupPage from '@/app/pages/ProfileSetup';
import DashboardPage from '@/app/pages/Dashboard';

// 路由守卫组件
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isProfileComplete } = useUserStore();
  
  if (!isProfileComplete) {
    return <Navigate to="/setup" replace />;
  }
  
  return <>{children}</>;
};

// 设置页面守卫
const SetupRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isProfileComplete } = useUserStore();
  
  if (isProfileComplete) {
    return <Navigate to="/dashboard" replace />;
  }
  
  return <>{children}</>;
};

// 根路由重定向
const RootRedirect: React.FC = () => {
  const { isProfileComplete } = useUserStore();
  
  return <Navigate to={isProfileComplete ? "/dashboard" : "/setup"} replace />;
};

export const router = createBrowserRouter([
  {
    path: "/",
    element: <RootRedirect />
  },
  {
    path: "/setup",
    element: (
      <SetupRoute>
        <ProfileSetupPage />
      </SetupRoute>
    )
  },
  {
    path: "/dashboard",
    element: (
      <ProtectedRoute>
        <DashboardPage />
      </ProtectedRoute>
    )
  },
  {
    path: "*",
    element: <Navigate to="/" replace />
  }
]);