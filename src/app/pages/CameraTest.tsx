import React, { useState } from 'react';
import { ImagePicker, ImagePreview, Card, CardHeader, CardTitle, CardContent, Button } from '@/shared/components';
import { formatFileSize } from '@/shared/utils/format';

const CameraTestPage: React.FC = () => {
  const [selectedImages, setSelectedImages] = useState<Array<{
    file: File;
    thumbnail: string;
    id: string;
  }>>([]);

  const handleImageSelect = (file: File, thumbnail?: string) => {
    const newImage = {
      file,
      thumbnail: thumbnail || URL.createObjectURL(file),
      id: `img_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };
    
    setSelectedImages(prev => [...prev, newImage]);
  };

  const handleImageDelete = (id: string) => {
    setSelectedImages(prev => prev.filter(img => img.id !== id));
  };

  const clearAllImages = () => {
    setSelectedImages([]);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-md mx-auto space-y-6">
        {/* 页面标题 */}
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            相机功能测试
          </h1>
          <p className="text-gray-600">
            测试相机拍照和图片选择功能
          </p>
        </div>

        {/* 图片选择器 */}
        <Card>
          <CardHeader>
            <CardTitle>添加图片</CardTitle>
          </CardHeader>
          <CardContent>
            <ImagePicker
              onImageSelect={handleImageSelect}
              maxSizeMB={2}
              placeholder="拍照或选择食物图片"
            />
          </CardContent>
        </Card>

        {/* 已选择的图片 */}
        {selectedImages.length > 0 && (
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>已选择的图片 ({selectedImages.length})</CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearAllImages}
                >
                  清空全部
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {selectedImages.map((image) => (
                  <div key={image.id} className="border border-gray-200 rounded-lg p-3">
                    <div className="flex gap-3">
                      {/* 图片预览 */}
                      <div className="w-20 h-20 flex-shrink-0">
                        <ImagePreview
                          src={image.thumbnail}
                          alt="选择的图片"
                          className="w-full h-full"
                          onDelete={() => handleImageDelete(image.id)}
                        />
                      </div>
                      
                      {/* 图片信息 */}
                      <div className="flex-1 space-y-1">
                        <div className="text-sm font-medium text-gray-900">
                          {image.file.name}
                        </div>
                        <div className="text-xs text-gray-500">
                          大小: {formatFileSize(image.file.size)}
                        </div>
                        <div className="text-xs text-gray-500">
                          类型: {image.file.type}
                        </div>
                        <div className="text-xs text-gray-500">
                          修改时间: {new Date(image.file.lastModified).toLocaleString()}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* 功能说明 */}
        <Card>
          <CardHeader>
            <CardTitle>功能说明</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm text-gray-600">
              <div>
                <h4 className="font-medium text-gray-900 mb-1">📷 拍照功能</h4>
                <p>支持前后摄像头切换，自动对焦，实时预览</p>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 mb-1">🖼️ 相册选择</h4>
                <p>支持从设备相册选择图片，支持多种格式</p>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 mb-1">🗜️ 自动压缩</h4>
                <p>图片自动压缩至2MB以下，保持清晰度</p>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 mb-1">🔍 图片预览</h4>
                <p>支持全屏查看，缩放，拖拽移动</p>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 mb-1">📱 移动端优化</h4>
                <p>触摸友好，响应式设计，PWA支持</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 技术信息 */}
        <Card>
          <CardHeader>
            <CardTitle>技术信息</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-xs text-gray-500">
              <div className="flex justify-between">
                <span>相机API支持:</span>
                <span className={navigator.mediaDevices ? 'text-green-600' : 'text-red-600'}>
                  {navigator.mediaDevices ? '✓ 支持' : '✗ 不支持'}
                </span>
              </div>
              <div className="flex justify-between">
                <span>文件API支持:</span>
                <span className={window.File ? 'text-green-600' : 'text-red-600'}>
                  {window.File ? '✓ 支持' : '✗ 不支持'}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Canvas API支持:</span>
                <span className={document.createElement('canvas').getContext ? 'text-green-600' : 'text-red-600'}>
                  {document.createElement('canvas').getContext ? '✓ 支持' : '✗ 不支持'}
                </span>
              </div>
              <div className="flex justify-between">
                <span>用户代理:</span>
                <span className="text-right max-w-48 truncate">
                  {navigator.userAgent.split(' ')[0]}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default CameraTestPage;