import React, { useState } from 'react';
import { FoodRecognition } from '@/domains/food';
import { Card, CardHeader, CardTitle, CardContent, Badge, Button } from '@/shared/components';
import { geminiService } from '@/infrastructure/ai';
import { formatCalories, formatWeight } from '@/shared/utils';

const AITestPage: React.FC = () => {
  const [selectedFoods, setSelectedFoods] = useState<Array<{
    name: string;
    calories: number;
    weight: number;
    confidence: number;
    timestamp: Date;
  }>>([]);

  const handleFoodSelect = (food: {
    name: string;
    calories: number;
    weight: number;
    confidence: number;
  }) => {
    const newFood = {
      ...food,
      timestamp: new Date()
    };
    
    setSelectedFoods(prev => [newFood, ...prev]);
  };

  const clearHistory = () => {
    setSelectedFoods([]);
  };

  const apiConfig = geminiService.getConfig();

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-md mx-auto space-y-6">
        {/* 页面标题 */}
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            AI食物识别测试
          </h1>
          <p className="text-gray-600">
            测试Google Gemini AI食物识别功能
          </p>
        </div>

        {/* API配置状态 */}
        <Card>
          <CardHeader>
            <CardTitle>API配置状态</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">API密钥</span>
                <Badge variant={apiConfig.hasApiKey ? 'success' : 'danger'}>
                  {apiConfig.hasApiKey ? '已配置' : '未配置'}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">API端点</span>
                <span className="text-xs text-gray-500 max-w-32 truncate">
                  {apiConfig.endpoint}
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">模型</span>
                <span className="text-xs text-gray-500">
                  {apiConfig.model}
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">超时时间</span>
                <span className="text-xs text-gray-500">
                  {apiConfig.timeout / 1000}秒
                </span>
              </div>
            </div>

            {!apiConfig.hasApiKey && (
              <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p className="text-sm text-yellow-700">
                  请在 .env 文件中配置 VITE_GEMINI_API_KEY 以启用AI功能
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* 食物识别组件 */}
        <FoodRecognition onFoodSelect={handleFoodSelect} />

        {/* 识别历史 */}
        {selectedFoods.length > 0 && (
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>识别历史 ({selectedFoods.length})</CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearHistory}
                >
                  清空
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {selectedFoods.map((food, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-medium text-gray-900">{food.name}</h4>
                          <Badge
                            size="sm"
                            variant={
                              food.confidence >= 0.8 ? 'success' :
                              food.confidence >= 0.6 ? 'warning' : 'danger'
                            }
                          >
                            {Math.round(food.confidence * 100)}%
                          </Badge>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-2 text-sm text-gray-600">
                          <div>{formatCalories(food.calories)}</div>
                          <div>{formatWeight(food.weight)}</div>
                        </div>
                        
                        <div className="text-xs text-gray-500 mt-1">
                          {food.timestamp.toLocaleTimeString()}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* 使用说明 */}
        <Card>
          <CardHeader>
            <CardTitle>使用说明</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm text-gray-600">
              <div>
                <h4 className="font-medium text-gray-900 mb-1">🤖 AI识别</h4>
                <p>基于Google Gemini AI，支持多种食物识别</p>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 mb-1">📸 图片要求</h4>
                <p>清晰的食物图片，光线充足，食物完整可见</p>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 mb-1">🎯 置信度</h4>
                <p>绿色：高置信度(≥80%)，黄色：中等(60-80%)，红色：低(&lt;60%)</p>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 mb-1">⚡ 性能</h4>
                <p>识别时间通常在3-10秒，取决于网络和图片大小</p>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 mb-1">🔒 隐私</h4>
                <p>图片仅用于AI识别，不会存储在服务器</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 技术信息 */}
        <Card>
          <CardHeader>
            <CardTitle>技术信息</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-xs text-gray-500">
              <div className="flex justify-between">
                <span>AI模型:</span>
                <span>{apiConfig.model}</span>
              </div>
              <div className="flex justify-between">
                <span>支持格式:</span>
                <span>JPEG, PNG, WebP</span>
              </div>
              <div className="flex justify-between">
                <span>最大文件:</span>
                <span>2MB (自动压缩)</span>
              </div>
              <div className="flex justify-between">
                <span>识别语言:</span>
                <span>中文 + 英文</span>
              </div>
              <div className="flex justify-between">
                <span>网络状态:</span>
                <span className={navigator.onLine ? 'text-green-600' : 'text-red-600'}>
                  {navigator.onLine ? '在线' : '离线'}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AITestPage;