import React from 'react';
import { useNavigate } from 'react-router-dom';
import ProfileSetupForm from '@/domains/user/components/ProfileSetupForm';
import { useUserStore } from '@/domains/user/stores/userStore';
import { CreateUserProfileForm } from '@/shared/types';
import { Toast } from '@/shared/components';

const ProfileSetupPage: React.FC = () => {
  const navigate = useNavigate();
  const { createProfile, loading, error, setError } = useUserStore();
  const [showSuccessToast, setShowSuccessToast] = React.useState(false);

  const handleSubmit = async (data: CreateUserProfileForm) => {
    try {
      await createProfile(data);
      setShowSuccessToast(true);
      
      // 延迟跳转到主页面
      setTimeout(() => {
        navigate('/dashboard');
      }, 2000);
    } catch (error) {
      console.error('创建档案失败:', error);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-md mx-auto">
        {/* 页面标题 */}
        <div className="text-center mb-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            设置个人档案
          </h1>
          <p className="text-gray-600">
            请填写您的基本信息，我们将为您制定个性化的卡路里计划
          </p>
        </div>

        {/* 表单组件 */}
        <ProfileSetupForm 
          onSubmit={handleSubmit}
          loading={loading}
        />

        {/* 错误提示 */}
        {error && (
          <div className="fixed top-4 right-4 z-50">
            <Toast
              type="error"
              title="设置失败"
              message={error}
              isVisible={!!error}
              onClose={() => setError(null)}
            />
          </div>
        )}

        {/* 成功提示 */}
        {showSuccessToast && (
          <div className="fixed top-4 right-4 z-50">
            <Toast
              type="success"
              title="设置成功"
              message="正在跳转到主页面..."
              isVisible={showSuccessToast}
              onClose={() => setShowSuccessToast(false)}
              duration={2000}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default ProfileSetupPage;