import React, { useEffect } from 'react';
import { useUserStore } from '@/domains/user/stores/userStore';
import { useNutritionStore } from '@/domains/nutrition/stores/nutritionStore';
import { NutritionCard, CalorieRing, NutritionAdvice } from '@/domains/nutrition';
import { <PERSON>, CardHeader, CardTitle, CardContent, Badge, Button } from '@/shared/components';
import { formatCalories, formatWeight, formatDate } from '@/shared/utils';

const DashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const { profile, clearProfile } = useUserStore();
  const { getDailySummary, updateDailySummary } = useNutritionStore();

  const today = new Date();
  const todaySummary = getDailySummary(today);

  // 初始化今日营养数据
  useEffect(() => {
    if (profile && !todaySummary) {
      // 创建默认的每日汇总
      updateDailySummary(today, {
        totalCalories: 0,
        calorieLimit: profile.dailyCalorieLimit,
        remainingCalories: profile.dailyCalorieLimit,
        mealBreakdown: {
          breakfast: {
            mealType: 'breakfast',
            calories: 0,
            calorieLimit: Math.round(profile.dailyCalorieLimit * profile.mealRatios.breakfast),
            foodCount: 0,
            percentage: 0
          },
          lunch: {
            mealType: 'lunch',
            calories: 0,
            calorieLimit: Math.round(profile.dailyCalorieLimit * profile.mealRatios.lunch),
            foodCount: 0,
            percentage: 0
          },
          dinner: {
            mealType: 'dinner',
            calories: 0,
            calorieLimit: Math.round(profile.dailyCalorieLimit * profile.mealRatios.dinner),
            foodCount: 0,
            percentage: 0
          },
          snack: {
            mealType: 'snack',
            calories: 0,
            calorieLimit: 0,
            foodCount: 0,
            percentage: 0
          }
        },
        nutrition: {
          protein: 0,
          fat: 0,
          carbs: 0,
          fiber: 0,
          sugar: 0
        },
        status: 'under',
        percentage: 0
      });
    }
  }, [profile, todaySummary, updateDailySummary, today]);

  if (!profile) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            未找到用户档案
          </h2>
          <p className="text-gray-600">
            请先完成个人档案设置
          </p>
        </div>
      </div>
    );
  }

  const weightLoss = profile.weight - profile.targetWeight;
  const weeklyLoss = (weightLoss / profile.targetDays) * 7;

  // 模拟添加一些测试数据
  const addTestData = () => {
    if (todaySummary) {
      const testCalories = 650;
      updateDailySummary(today, {
        totalCalories: todaySummary.totalCalories + testCalories,
        mealBreakdown: {
          ...todaySummary.mealBreakdown,
          breakfast: {
            ...todaySummary.mealBreakdown.breakfast,
            calories: todaySummary.mealBreakdown.breakfast.calories + testCalories,
            foodCount: todaySummary.mealBreakdown.breakfast.foodCount + 1
          }
        },
        nutrition: {
          protein: todaySummary.nutrition.protein + 25,
          fat: todaySummary.nutrition.fat + 15,
          carbs: todaySummary.nutrition.carbs + 45,
          fiber: todaySummary.nutrition.fiber + 5,
          sugar: todaySummary.nutrition.sugar + 10
        }
      });
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-md mx-auto space-y-6">
        {/* 欢迎信息 */}
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            {profile.name ? `你好，${profile.name}` : '欢迎使用 KCal Tracker'}
          </h1>
          <p className="text-gray-600">
            {formatDate(today, 'yyyy年MM月dd日')} · 今天是您健康之旅的新一天
          </p>
        </div>

        {/* 今日营养概览 */}
        {todaySummary && (
          <>
            {/* 卡路里环形图 */}
            <div className="flex justify-center">
              <CalorieRing
                current={todaySummary.totalCalories}
                target={todaySummary.calorieLimit}
                size="lg"
              />
            </div>

            {/* 营养卡片 */}
            <NutritionCard summary={todaySummary} />

            {/* 营养建议 */}
            <NutritionAdvice summary={todaySummary} profile={profile} />
          </>
        )}

        {/* 用户档案卡片 */}
        <Card>
          <CardHeader>
            <CardTitle>个人档案</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* 基本信息 */}
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <div className="text-2xl font-bold text-gray-900">
                    {profile.height}
                  </div>
                  <div className="text-sm text-gray-500">身高 (cm)</div>
                </div>
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <div className="text-2xl font-bold text-gray-900">
                    {profile.weight}
                  </div>
                  <div className="text-sm text-gray-500">体重 (kg)</div>
                </div>
              </div>

              {/* 目标信息 */}
              <div className="bg-primary-50 rounded-lg p-4">
                <h4 className="font-medium text-primary-900 mb-2">减重目标</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-primary-700">目标体重：</span>
                    <span className="font-medium">{formatWeight(profile.targetWeight, 'kg')}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-primary-700">需要减重：</span>
                    <span className="font-medium">{formatWeight(weightLoss, 'kg')}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-primary-700">目标天数：</span>
                    <span className="font-medium">{profile.targetDays} 天</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-primary-700">每周减重：</span>
                    <span className="font-medium">{formatWeight(weeklyLoss, 'kg')}</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 营养计划卡片 */}
        <Card>
          <CardHeader>
            <CardTitle>营养计划</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* 每日卡路里限额 */}
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-3xl font-bold text-green-700">
                  {formatCalories(profile.dailyCalorieLimit)}
                </div>
                <div className="text-sm text-green-600">每日卡路里限额</div>
              </div>

              {/* 三餐分配 */}
              <div className="space-y-2">
                <h4 className="font-medium text-gray-900">三餐分配</h4>
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">早餐</span>
                    <div className="flex items-center gap-2">
                      <Badge variant="default">
                        {Math.round(profile.mealRatios.breakfast * 100)}%
                      </Badge>
                      <span className="font-medium">
                        {formatCalories(profile.dailyCalorieLimit * profile.mealRatios.breakfast)}
                      </span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">午餐</span>
                    <div className="flex items-center gap-2">
                      <Badge variant="default">
                        {Math.round(profile.mealRatios.lunch * 100)}%
                      </Badge>
                      <span className="font-medium">
                        {formatCalories(profile.dailyCalorieLimit * profile.mealRatios.lunch)}
                      </span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">晚餐</span>
                    <div className="flex items-center gap-2">
                      <Badge variant="default">
                        {Math.round(profile.mealRatios.dinner * 100)}%
                      </Badge>
                      <span className="font-medium">
                        {formatCalories(profile.dailyCalorieLimit * profile.mealRatios.dinner)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* 代谢信息 */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">代谢信息</h4>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">基础代谢率 (BMR)：</span>
                    <span className="font-medium">{formatCalories(profile.bmr)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">总日消耗 (TDEE)：</span>
                    <span className="font-medium">{formatCalories(profile.tdee)}</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 操作按钮 */}
        <div className="space-y-2">
          <Button
            variant="primary"
            fullWidth
            onClick={() => alert('食物记录功能即将推出')}
          >
            开始记录食物
          </Button>
          <Button
            variant="secondary"
            fullWidth
            onClick={addTestData}
          >
            添加测试数据
          </Button>
          <Button
            variant="secondary"
            fullWidth
            onClick={() => navigate('/calendar')}
          >
            查看日历
          </Button>
          <Button
            variant="ghost"
            fullWidth
            onClick={clearProfile}
          >
            重新设置档案
          </Button>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;