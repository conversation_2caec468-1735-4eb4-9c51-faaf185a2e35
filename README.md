# KCal Tracker - AI驱动的智能卡路里追踪应用

<div align="center">

![KCal Tracker](https://img.shields.io/badge/KCal%20Tracker-v1.0.0-blue)
![React](https://img.shields.io/badge/React-18.2+-61DAFB?logo=react)
![TypeScript](https://img.shields.io/badge/TypeScript-5.0+-3178C6?logo=typescript)
![Vite](https://img.shields.io/badge/Vite-7.0+-646CFF?logo=vite)
![PWA](https://img.shields.io/badge/PWA-Ready-5A0FC8)

**现代化移动端Web应用，集成AI食物识别，助力科学减重**

[在线演示](http://localhost:5176/) • [功能特性](#功能特性) • [技术栈](#技术栈) • [快速开始](#快速开始)

</div>

## 🎯 项目概述

KCal Tracker是一款基于React 18和TypeScript开发的现代化移动端Web应用，集成Google Gemini AI食物识别技术，为用户提供科学、智能、便捷的卡路里追踪和营养管理服务。

### 🏆 核心价值

- **🧠 AI驱动**: Google Gemini 2.0 Flash食物识别
- **📱 移动优先**: 完美的移动端用户体验
- **📊 数据洞察**: 可视化营养分析和趋势
- **🔬 科学计算**: 基于BMR的精确卡路里计算
- **⚡ 现代技术**: React 18 + TypeScript + PWA

## ✨ 功能特性

### 🤖 AI食物识别
- **智能识别**: 拍照即可识别食物种类和营养信息
- **营养估算**: 自动计算卡路里、重量和营养成分
- **置信度评估**: 提供识别准确度和备选结果
- **多食物支持**: 同时识别图片中的多种食物

### 👤 用户档案管理
- **多步骤表单**: 引导式用户信息收集
- **BMR自动计算**: 基于Mifflin-St Jeor公式
- **目标设置**: 个性化减重目标和时间规划
- **安全验证**: 减重速度安全性检查

### 📊 营养分析系统
- **实时追踪**: 卡路里、蛋白质、脂肪、碳水化合物
- **智能建议**: 基于摄入量的动态营养建议
- **三餐分配**: 科学的三餐卡路里分配监控
- **可视化展示**: 环形进度图、营养分布图

### 📅 交互式日历
- **颜色编码**: 直观的营养状态指示
- **趋势分析**: 周度/月度数据可视化
- **详细查看**: 点击日期查看完整营养信息
- **Chart.js集成**: 专业的图表展示

### 📷 相机集成
- **实时拍照**: MediaDevices API相机集成
- **图片处理**: 智能压缩和尺寸优化
- **全屏预览**: 缩放、拖拽、手势控制
- **相册选择**: 支持从设备相册选择图片

### 💾 本地存储
- **IndexedDB**: 高性能本地数据库
- **Repository模式**: 清晰的数据访问层
- **数据导出**: 支持数据备份和迁移
- **离线支持**: PWA离线功能准备

## 🛠️ 技术栈

### 前端框架
- **React 18.2+** - 最新的React特性和性能优化
- **TypeScript 5.0+** - 严格类型检查和开发体验
- **Vite 7.0+** - 快速构建和热重载

### UI/UX
- **Tailwind CSS 4.1+** - 原子化CSS框架
- **Headless UI** - 无障碍UI组件库
- **Heroicons** - 高质量SVG图标

### 状态管理
- **Zustand 5.0+** - 轻量级状态管理
- **持久化中间件** - 自动数据保存

### 数据可视化
- **Chart.js 4.5+** - 专业图表库
- **React Chart.js 2** - React集成
- **自定义SVG** - 环形进度图

### AI服务
- **Google Gemini 2.0 Flash** - 最新的多模态AI模型
- **结构化提示词** - 优化的食物识别提示
- **置信度评估** - 智能结果验证

### 数据存储
- **IndexedDB** - 浏览器本地数据库
- **Repository模式** - 数据访问层抽象
- **数据验证** - 完整的类型安全

### PWA功能
- **Service Worker** - 离线缓存和后台同步
- **Web App Manifest** - 原生应用体验
- **响应式设计** - 320px-768px完美适配

## 🚀 快速开始

### 环境要求
- Node.js 18+
- npm 9+ 或 yarn 1.22+

### 安装依赖
```bash
npm install
```

### 环境配置
复制环境变量示例文件：
```bash
cp .env.example .env
```

配置Google Gemini API密钥：
```env
VITE_GEMINI_API_KEY=your_gemini_api_key_here
```

### 启动开发服务器
```bash
npm run dev
```

应用将在 http://localhost:5173 启动

### 构建生产版本
```bash
npm run build
```

### 预览生产版本
```bash
npm run preview
```

## 📱 使用指南

### 1. 设置个人档案
- 填写身高、体重、年龄、性别等基本信息
- 设置减重目标和时间周期
- 选择活动水平
- 系统自动计算每日卡路里限额

### 2. AI食物识别
- 点击"拍照"或"从相册选择"
- 拍摄或选择食物图片
- AI自动识别食物并估算营养信息
- 选择识别结果添加到记录

### 3. 营养追踪
- 查看每日卡路里摄入进度
- 监控三餐分配情况
- 获取智能营养建议
- 分析营养素分布

### 4. 数据分析
- 使用日历查看历史记录
- 分析周度/月度趋势
- 查看详细营养统计
- 导出数据备份

## 🏗️ 项目结构

```
src/
├── app/                    # 应用层
│   ├── pages/             # 页面组件
│   └── router/            # 路由配置
├── domains/               # 业务域
│   ├── user/             # 用户域
│   ├── nutrition/        # 营养域
│   ├── food/             # 食物域
│   └── analytics/        # 分析域
├── shared/               # 共享层
│   ├── components/       # UI组件库
│   ├── types/           # 类型定义
│   └── utils/           # 工具函数
└── infrastructure/      # 基础设施层
    ├── storage/         # 数据存储
    └── ai/             # AI服务
```

## 🎨 设计系统

### 组件架构
- **原子级组件**: Button, Input, Card, Badge, Spinner
- **分子级组件**: FormField, Modal, Toast, Camera, ImagePicker
- **页面级组件**: Dashboard, Calendar, ProfileSetup

### 颜色系统
- **主色调**: Primary (蓝色系)
- **状态色**: Success (绿), Warning (黄), Danger (红)
- **中性色**: Gray (灰色系)

### 响应式断点
- **Mobile**: 320px - 768px
- **Tablet**: 768px - 1024px
- **Desktop**: 1024px+

## 📊 性能指标

### 构建优化
- **代码分割**: 按路由和功能模块分包
- **Tree Shaking**: 自动移除未使用代码
- **资源压缩**: 图片和代码自动压缩

### 运行时性能
- **懒加载**: 组件和路由按需加载
- **缓存策略**: 智能数据缓存
- **内存管理**: 高效的状态管理

### 移动端优化
- **触摸响应**: <100ms触摸延迟
- **图片处理**: 智能压缩至2MB以下
- **电池优化**: 减少不必要的重渲染

## 🔧 开发工具

### 代码质量
- **ESLint** - 代码质量检查
- **Prettier** - 代码格式化
- **Husky** - Git hooks
- **TypeScript** - 类型检查

### 开发体验
- **热重载** - 实时代码更新
- **路径别名** - 简化导入路径
- **错误边界** - 优雅的错误处理
- **开发工具** - React DevTools支持

## 🧪 测试

### 测试页面
- `/camera-test` - 相机功能测试
- `/ai-test` - AI识别功能测试

### 兼容性测试
- **iOS Safari 14+**
- **Android Chrome 90+**
- **PWA功能验证**

## 📈 项目进度

- ✅ **项目初始化与开发环境配置** (100%)
- ✅ **TypeScript类型系统与数据模型设计** (100%)
- ✅ **基础工具函数开发** (100%)
- ✅ **基础UI组件库开发** (100%)
- ✅ **用户档案管理系统** (100%)
- ✅ **BMR计算引擎与营养分析系统** (100%)
- ✅ **本地数据存储系统** (100%)
- ✅ **交互式日历仪表板** (100%)
- ✅ **相机集成与图片处理系统** (100%)
- ✅ **Google Gemini AI食物识别服务** (100%)
- 🔄 **食物条目管理系统** (进行中)
- ⏳ **PWA功能完善与离线支持**
- ⏳ **性能优化与测试覆盖**

**总体进度**: 77% (10/13 模块完成)

## 🤝 贡献指南

### 开发流程
1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

### 代码规范
- 遵循 ESLint 和 Prettier 配置
- 使用 TypeScript 严格模式
- 编写清晰的注释和文档
- 保持组件的单一职责

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- [React](https://reactjs.org/) - 用户界面库
- [TypeScript](https://www.typescriptlang.org/) - 类型安全
- [Tailwind CSS](https://tailwindcss.com/) - CSS框架
- [Chart.js](https://www.chartjs.org/) - 图表库
- [Google Gemini](https://ai.google.dev/) - AI服务
- [Heroicons](https://heroicons.com/) - 图标库

## 📞 联系方式

- 项目地址: [GitHub Repository](https://github.com/your-username/kcal-tracker)
- 问题反馈: [Issues](https://github.com/your-username/kcal-tracker/issues)
- 功能建议: [Discussions](https://github.com/your-username/kcal-tracker/discussions)

---

<div align="center">

**⭐ 如果这个项目对你有帮助，请给它一个星标！**

Made with ❤️ by [Your Name](https://github.com/your-username)

</div>