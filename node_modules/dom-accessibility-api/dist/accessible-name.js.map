{"version": 3, "file": "accessible-name.js", "names": ["prohibitsNaming", "node", "hasAnyConcreteRoles", "computeAccessibleName", "root", "options", "computeTextAlternative"], "sources": ["../sources/accessible-name.ts"], "sourcesContent": ["import {\n\tcomputeTextAlternative,\n\tComputeTextAlternativeOptions,\n} from \"./accessible-name-and-description\";\nimport { hasAnyConcreteRoles } from \"./util\";\n\n/**\n * https://w3c.github.io/aria/#namefromprohibited\n */\nfunction prohibitsNaming(node: Node): boolean {\n\treturn hasAnyConcreteRoles(node, [\n\t\t\"caption\",\n\t\t\"code\",\n\t\t\"deletion\",\n\t\t\"emphasis\",\n\t\t\"generic\",\n\t\t\"insertion\",\n\t\t\"paragraph\",\n\t\t\"presentation\",\n\t\t\"strong\",\n\t\t\"subscript\",\n\t\t\"superscript\",\n\t]);\n}\n\n/**\n * implements https://w3c.github.io/accname/#mapping_additional_nd_name\n * @param root\n * @param options\n * @returns\n */\nexport function computeAccessibleName(\n\troot: Element,\n\toptions: ComputeTextAlternativeOptions = {}\n): string {\n\tif (prohibitsNaming(root)) {\n\t\treturn \"\";\n\t}\n\n\treturn computeTextAlternative(root, options);\n}\n"], "mappings": ";;;;AAAA;AAIA;AAEA;AACA;AACA;AACA,SAASA,eAAe,CAACC,IAAU,EAAW;EAC7C,OAAO,IAAAC,yBAAmB,EAACD,IAAI,EAAE,CAChC,SAAS,EACT,MAAM,EACN,UAAU,EACV,UAAU,EACV,SAAS,EACT,WAAW,EACX,WAAW,EACX,cAAc,EACd,QAAQ,EACR,WAAW,EACX,aAAa,CACb,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAASE,qBAAqB,CACpCC,IAAa,EAEJ;EAAA,IADTC,OAAsC,uEAAG,CAAC,CAAC;EAE3C,IAAIL,eAAe,CAACI,IAAI,CAAC,EAAE;IAC1B,OAAO,EAAE;EACV;EAEA,OAAO,IAAAE,oDAAsB,EAACF,IAAI,EAAEC,OAAO,CAAC;AAC7C"}