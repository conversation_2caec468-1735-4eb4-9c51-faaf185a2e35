{"version": 3, "file": "util.mjs", "names": ["getLocalName", "getRole", "presentationRoles", "isElement", "node", "nodeType", "ELEMENT_NODE", "isHTMLTableCaptionElement", "isHTMLInputElement", "isHTMLOptGroupElement", "isHTMLSelectElement", "isHTMLTableElement", "isHTMLTextAreaElement", "safeWindow", "_ref", "ownerDocument", "defaultView", "TypeError", "isHTMLFieldSetElement", "isHTMLLegendElement", "isHTMLSlotElement", "isSVGElement", "ownerSVGElement", "undefined", "isSVGSVGElement", "isSVGTitleElement", "queryIdRefs", "attributeName", "hasAttribute", "ids", "getAttribute", "split", "root", "getRootNode", "map", "id", "getElementById", "filter", "element", "hasAnyConcreteRoles", "roles", "indexOf"], "sources": ["../sources/util.ts"], "sourcesContent": ["export { getLocalName } from \"./getRole\";\nimport getRole, { getLocalName } from \"./getRole\";\n\nexport const presentationRoles = [\"presentation\", \"none\"];\n\nexport function isElement(node: Node | null): node is Element {\n\treturn node !== null && node.nodeType === node.ELEMENT_NODE;\n}\n\nexport function isHTMLTableCaptionElement(\n\tnode: Node | null,\n): node is HTMLTableCaptionElement {\n\treturn isElement(node) && getLocalName(node) === \"caption\";\n}\n\nexport function isHTMLInputElement(\n\tnode: Node | null,\n): node is HTMLInputElement {\n\treturn isElement(node) && getLocalName(node) === \"input\";\n}\n\nexport function isHTMLOptGroupElement(\n\tnode: Node | null,\n): node is HTMLOptGroupElement {\n\treturn isElement(node) && getLocalName(node) === \"optgroup\";\n}\n\nexport function isHTMLSelectElement(\n\tnode: Node | null,\n): node is HTMLSelectElement {\n\treturn isElement(node) && getLocalName(node) === \"select\";\n}\n\nexport function isHTMLTableElement(\n\tnode: Node | null,\n): node is HTMLTableElement {\n\treturn isElement(node) && getLocalName(node) === \"table\";\n}\n\nexport function isHTMLTextAreaElement(\n\tnode: Node | null,\n): node is HTMLTextAreaElement {\n\treturn isElement(node) && getLocalName(node) === \"textarea\";\n}\n\nexport function safeWindow(node: Node): Window {\n\tconst { defaultView } =\n\t\tnode.ownerDocument === null ? (node as Document) : node.ownerDocument;\n\n\tif (defaultView === null) {\n\t\tthrow new TypeError(\"no window available\");\n\t}\n\treturn defaultView;\n}\n\nexport function isHTMLFieldSetElement(\n\tnode: Node | null,\n): node is HTMLFieldSetElement {\n\treturn isElement(node) && getLocalName(node) === \"fieldset\";\n}\n\nexport function isHTMLLegendElement(\n\tnode: Node | null,\n): node is HTMLLegendElement {\n\treturn isElement(node) && getLocalName(node) === \"legend\";\n}\n\nexport function isHTMLSlotElement(node: Node | null): node is HTMLSlotElement {\n\treturn isElement(node) && getLocalName(node) === \"slot\";\n}\n\nexport function isSVGElement(node: Node | null): node is SVGElement {\n\treturn isElement(node) && (node as SVGElement).ownerSVGElement !== undefined;\n}\n\nexport function isSVGSVGElement(node: Node | null): node is SVGSVGElement {\n\treturn isElement(node) && getLocalName(node) === \"svg\";\n}\n\nexport function isSVGTitleElement(node: Node | null): node is SVGTitleElement {\n\treturn isSVGElement(node) && getLocalName(node) === \"title\";\n}\n\n/**\n *\n * @param {Node} node -\n * @param {string} attributeName -\n * @returns {Element[]} -\n */\nexport function queryIdRefs(node: Node, attributeName: string): Element[] {\n\tif (isElement(node) && node.hasAttribute(attributeName)) {\n\t\t// eslint-disable-next-line @typescript-eslint/no-non-null-assertion -- safe due to hasAttribute check\n\t\tconst ids = node.getAttribute(attributeName)!.split(\" \");\n\n\t\t// Browsers that don't support shadow DOM won't have getRootNode\n\t\tconst root = node.getRootNode\n\t\t\t? (node.getRootNode() as Document | ShadowRoot)\n\t\t\t: node.ownerDocument;\n\n\t\treturn ids\n\t\t\t.map((id) => root.getElementById(id))\n\t\t\t.filter(\n\t\t\t\t(element: Element | null): element is Element => element !== null,\n\t\t\t\t// TODO: why does this not narrow?\n\t\t\t) as Element[];\n\t}\n\n\treturn [];\n}\n\nexport function hasAnyConcreteRoles(\n\tnode: Node,\n\troles: Array<string | null>,\n): node is Element {\n\tif (isElement(node)) {\n\t\treturn roles.indexOf(getRole(node)) !== -1;\n\t}\n\treturn false;\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,eAAW;AACxC,OAAOC,OAAO,IAAID,YAAY,QAAQ,eAAW;AAEjD,OAAO,IAAME,iBAAiB,GAAG,CAAC,cAAc,EAAE,MAAM,CAAC;AAEzD,OAAO,SAASC,SAASA,CAACC,IAAiB,EAAmB;EAC7D,OAAOA,IAAI,KAAK,IAAI,IAAIA,IAAI,CAACC,QAAQ,KAAKD,IAAI,CAACE,YAAY;AAC5D;AAEA,OAAO,SAASC,yBAAyBA,CACxCH,IAAiB,EACiB;EAClC,OAAOD,SAAS,CAACC,IAAI,CAAC,IAAIJ,YAAY,CAACI,IAAI,CAAC,KAAK,SAAS;AAC3D;AAEA,OAAO,SAASI,kBAAkBA,CACjCJ,IAAiB,EACU;EAC3B,OAAOD,SAAS,CAACC,IAAI,CAAC,IAAIJ,YAAY,CAACI,IAAI,CAAC,KAAK,OAAO;AACzD;AAEA,OAAO,SAASK,qBAAqBA,CACpCL,IAAiB,EACa;EAC9B,OAAOD,SAAS,CAACC,IAAI,CAAC,IAAIJ,YAAY,CAACI,IAAI,CAAC,KAAK,UAAU;AAC5D;AAEA,OAAO,SAASM,mBAAmBA,CAClCN,IAAiB,EACW;EAC5B,OAAOD,SAAS,CAACC,IAAI,CAAC,IAAIJ,YAAY,CAACI,IAAI,CAAC,KAAK,QAAQ;AAC1D;AAEA,OAAO,SAASO,kBAAkBA,CACjCP,IAAiB,EACU;EAC3B,OAAOD,SAAS,CAACC,IAAI,CAAC,IAAIJ,YAAY,CAACI,IAAI,CAAC,KAAK,OAAO;AACzD;AAEA,OAAO,SAASQ,qBAAqBA,CACpCR,IAAiB,EACa;EAC9B,OAAOD,SAAS,CAACC,IAAI,CAAC,IAAIJ,YAAY,CAACI,IAAI,CAAC,KAAK,UAAU;AAC5D;AAEA,OAAO,SAASS,UAAUA,CAACT,IAAU,EAAU;EAC9C,IAAAU,IAAA,GACCV,IAAI,CAACW,aAAa,KAAK,IAAI,GAAIX,IAAI,GAAgBA,IAAI,CAACW,aAAa;IAD9DC,WAAW,GAAAF,IAAA,CAAXE,WAAW;EAGnB,IAAIA,WAAW,KAAK,IAAI,EAAE;IACzB,MAAM,IAAIC,SAAS,CAAC,qBAAqB,CAAC;EAC3C;EACA,OAAOD,WAAW;AACnB;AAEA,OAAO,SAASE,qBAAqBA,CACpCd,IAAiB,EACa;EAC9B,OAAOD,SAAS,CAACC,IAAI,CAAC,IAAIJ,YAAY,CAACI,IAAI,CAAC,KAAK,UAAU;AAC5D;AAEA,OAAO,SAASe,mBAAmBA,CAClCf,IAAiB,EACW;EAC5B,OAAOD,SAAS,CAACC,IAAI,CAAC,IAAIJ,YAAY,CAACI,IAAI,CAAC,KAAK,QAAQ;AAC1D;AAEA,OAAO,SAASgB,iBAAiBA,CAAChB,IAAiB,EAA2B;EAC7E,OAAOD,SAAS,CAACC,IAAI,CAAC,IAAIJ,YAAY,CAACI,IAAI,CAAC,KAAK,MAAM;AACxD;AAEA,OAAO,SAASiB,YAAYA,CAACjB,IAAiB,EAAsB;EACnE,OAAOD,SAAS,CAACC,IAAI,CAAC,IAAKA,IAAI,CAAgBkB,eAAe,KAAKC,SAAS;AAC7E;AAEA,OAAO,SAASC,eAAeA,CAACpB,IAAiB,EAAyB;EACzE,OAAOD,SAAS,CAACC,IAAI,CAAC,IAAIJ,YAAY,CAACI,IAAI,CAAC,KAAK,KAAK;AACvD;AAEA,OAAO,SAASqB,iBAAiBA,CAACrB,IAAiB,EAA2B;EAC7E,OAAOiB,YAAY,CAACjB,IAAI,CAAC,IAAIJ,YAAY,CAACI,IAAI,CAAC,KAAK,OAAO;AAC5D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASsB,WAAWA,CAACtB,IAAU,EAAEuB,aAAqB,EAAa;EACzE,IAAIxB,SAAS,CAACC,IAAI,CAAC,IAAIA,IAAI,CAACwB,YAAY,CAACD,aAAa,CAAC,EAAE;IACxD;IACA,IAAME,GAAG,GAAGzB,IAAI,CAAC0B,YAAY,CAACH,aAAa,CAAC,CAAEI,KAAK,CAAC,GAAG,CAAC;;IAExD;IACA,IAAMC,IAAI,GAAG5B,IAAI,CAAC6B,WAAW,GACzB7B,IAAI,CAAC6B,WAAW,CAAC,CAAC,GACnB7B,IAAI,CAACW,aAAa;IAErB,OAAOc,GAAG,CACRK,GAAG,CAAC,UAACC,EAAE;MAAA,OAAKH,IAAI,CAACI,cAAc,CAACD,EAAE,CAAC;IAAA,EAAC,CACpCE,MAAM,CACN,UAACC,OAAuB;MAAA,OAAyBA,OAAO,KAAK,IAAI;IAAA;IACjE;IACD,CAAC;EACH;;EAEA,OAAO,EAAE;AACV;AAEA,OAAO,SAASC,mBAAmBA,CAClCnC,IAAU,EACVoC,KAA2B,EACT;EAClB,IAAIrC,SAAS,CAACC,IAAI,CAAC,EAAE;IACpB,OAAOoC,KAAK,CAACC,OAAO,CAACxC,OAAO,CAACG,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC;EAC3C;EACA,OAAO,KAAK;AACb"}