"use strict";

exports.__esModule = true;
var _exportNames = {
  computeAccessibleDescription: true,
  computeAccessibleName: true,
  getRole: true,
  isDisabled: true
};
exports.isDisabled = exports.getRole = exports.computeAccessibleName = exports.computeAccessibleDescription = void 0;
var _accessibleDescription = require("./accessible-description");
exports.computeAccessibleDescription = _accessibleDescription.computeAccessibleDescription;
var _accessibleName = require("./accessible-name");
exports.computeAccessibleName = _accessibleName.computeAccessibleName;
var _getRole = _interopRequireDefault(require("./getRole"));
exports.getRole = _getRole.default;
var _isInaccessible = require("./is-inaccessible");
Object.keys(_isInaccessible).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _isInaccessible[key]) return;
  exports[key] = _isInaccessible[key];
});
var _isDisabled = require("./is-disabled");
exports.isDisabled = _isDisabled.isDisabled;
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
//# sourceMappingURL=index.js.map